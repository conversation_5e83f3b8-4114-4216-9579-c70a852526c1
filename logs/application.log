2025-08-26 13:50:31.291 [[background-preinit]] DEBUG org.jboss.logging - Logging Provider: org.jboss.logging.Log4j2LoggerProvider
2025-08-26 13:50:31.292 [[background-preinit]] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final
2025-08-26 13:50:31.295 [[main]] INFO  c.n.pro.NeoProxyApiApplication - Starting NeoProxyApiApplication using Java 17.0.16 on vinhbui-HP-EliteBook-845-14-inch-G10-Notebook-PC with PID 27154 (/home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes started by vinhbui8 in /home/<USER>/Documents/aaaproxy/neo-proxy_app)
2025-08-26 13:50:31.296 [[main]] INFO  c.n.pro.NeoProxyApiApplication - The following profiles are active: dev
2025-08-26 13:50:31.695 [[main]] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-26 13:50:31.742 [[main]] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 44 ms. Found 14 JPA repository interfaces.
2025-08-26 13:50:31.994 [[main]] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-26 13:50:31.999 [[main]] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-26 13:50:31.999 [[main]] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-26 13:50:31.999 [[main]] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.56]
2025-08-26 13:50:32.038 [[main]] INFO  o.a.c.c.C.[.[localhost].[/neoproxy] - Initializing Spring embedded WebApplicationContext
2025-08-26 13:50:32.038 [[main]] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 722 ms
2025-08-26 13:50:32.069 [[main]] DEBUG i.m.c.u.i.l.InternalLoggerFactory - Using SLF4J as the default logging framework
2025-08-26 13:50:32.147 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - Driver class org.postgresql.Driver found in Thread context class loader TomcatEmbeddedWebappClassLoader
  context: neoproxy
  delegate: true
----------> Parent Classloader:
jdk.internal.loader.ClassLoaders$AppClassLoader@30946e09

2025-08-26 13:50:32.179 [[main]] DEBUG o.f.c.internal.util.FeatureDetector - AWS SDK available: false
2025-08-26 13:50:32.179 [[main]] DEBUG o.f.c.internal.util.FeatureDetector - Google Cloud Storage available: false
2025-08-26 13:50:32.180 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/callback' ...
2025-08-26 13:50:32.180 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/callback using ClassLoader TomcatEmbeddedWebappClassLoader
  context: neoproxy
  delegate: true
----------> Parent Classloader:
jdk.internal.loader.ClassLoaders$AppClassLoader@30946e09
 ...
2025-08-26 13:50:32.180 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Unable to resolve location classpath:db/callback.
2025-08-26 13:50:32.180 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/migration' ...
2025-08-26 13:50:32.180 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/migration using ClassLoader TomcatEmbeddedWebappClassLoader
  context: neoproxy
  delegate: true
----------> Parent Classloader:
jdk.internal.loader.ClassLoaders$AppClassLoader@30946e09
 ...
2025-08-26 13:50:32.180 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning URL: file:/home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes/db/migration
2025-08-26 13:50:32.180 [[main]] DEBUG o.f.c.internal.util.FeatureDetector - JBoss VFS v2 available: false
2025-08-26 13:50:32.180 [[main]] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning starting at classpath root in filesystem: /home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes/
2025-08-26 13:50:32.180 [[main]] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning for resources in path: /home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes/db/migration (db/migration)
2025-08-26 13:50:32.181 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V10__CREATE_TABLE_LOCATIONS.sql
2025-08-26 13:50:32.181 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V11__CREATE_TABLE_TRACKINGS.sql
2025-08-26 13:50:32.181 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V12__UPDATE_TABLE_TRANSACTIONS.sql
2025-08-26 13:50:32.181 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V13__UPDATE_TABLE_PROXYS.sql
2025-08-26 13:50:32.181 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V14__UPDATE_TABLE_PROXYS.sql
2025-08-26 13:50:32.181 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V15__UPDATE_TABLE_TRANSACTION.sql
2025-08-26 13:50:32.181 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V16__UPDATE_TABLE_CONFIG.sql
2025-08-26 13:50:32.181 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V17__CREATE_TABLE_PROMOTION.sql
2025-08-26 13:50:32.181 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V18__UPDATE_TABLE_TRANSACTION.sql
2025-08-26 13:50:32.181 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V19__UPDATE_TABLE_LICENSES.sql
2025-08-26 13:50:32.182 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V1__CREATE_TABLE_USERS.sql
2025-08-26 13:50:32.182 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V20__UPDATE_TABLE_USER.sql
2025-08-26 13:50:32.182 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V21__UPDATE_TABLE_TRANSACTION.sql
2025-08-26 13:50:32.182 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V22__UPDATE_TABLE_CONFIG.sql
2025-08-26 13:50:32.182 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V23__CREATE_TABLE_MAIL_TEMPLATE.sql
2025-08-26 13:50:32.182 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V24__UPDATE_TABLE_PROXYS.sql
2025-08-26 13:50:32.182 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V25__UPDATE_TABLE_MAIL.sql
2025-08-26 13:50:32.182 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V2__CREATE_TABLE_MODEMS.sql
2025-08-26 13:50:32.182 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V31__UPDATE_TABLE_LICENSE_FOR_SELLING.sql
2025-08-26 13:50:32.182 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V32__CREATE_TABLE_PROXYS.sql
2025-08-26 13:50:32.182 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V33__UPDATE_TABLE_PROXY_FOR_ISP.sql
2025-08-26 13:50:32.182 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V34__UPDATE_TABLE_PROXY_FOR_SPEED_TEST.sql
2025-08-26 13:50:32.182 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V35__UPDATE_TABLE_PACKAGES.sql
2025-08-26 13:50:32.182 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V36__UPDATE_TABLE_LICENSES.sql
2025-08-26 13:50:32.182 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V37__UPDATE_TABLE_LICENSES.sql
2025-08-26 13:50:32.182 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V38__UPDATE_TABLE_MAIL.sql
2025-08-26 13:50:32.182 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V39__UPDATE_TABLE_TRANSACTION.sql
2025-08-26 13:50:32.182 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V3__CREATE_TABLE_PROXY_WANS.sql
2025-08-26 13:50:32.182 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V40__UPDATE_TABLE_PROMOTION.sql
2025-08-26 13:50:32.182 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V41__UPDATE_TABLE_LICENSES_AUTO_RENEWAL.sql
2025-08-26 13:50:32.182 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V42__UPDATE_TABLE_PACKAGES_ADD_LOCATION_FEE.sql
2025-08-26 13:50:32.182 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V4__CREATE_TABLE_LICENSES.sql
2025-08-26 13:50:32.182 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V5__CREATE_TABLE_PACKAGES.sql
2025-08-26 13:50:32.182 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V6__CREATE_TABLE_TRANSACTIONS.sql
2025-08-26 13:50:32.182 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V7__CREATE_TABLE_MONITORS.sql
2025-08-26 13:50:32.182 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V8__CREATE_TABLE_REFRESH_TOKENS.sql
2025-08-26 13:50:32.182 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V9__CREATE_TABLE_CONFIGURATIONS.sql
2025-08-26 13:50:32.182 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classes at classpath:db/migration
2025-08-26 13:50:32.183 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-08-26 13:50:32.184 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension................................false
2025-08-26 13:50:32.184 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit................................true
2025-08-26 13:50:32.184 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - catalog................................none
2025-08-26 13:50:32.184 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql................................none
2025-08-26 13:50:32.184 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery................................none
2025-08-26 13:50:32.184 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout................................30000
2025-08-26 13:50:32.184 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSource................................none
2025-08-26 13:50:32.184 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName................................none
2025-08-26 13:50:32.184 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI................................none
2025-08-26 13:50:32.184 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties................................{password=<masked>}
2025-08-26 13:50:32.184 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................................"org.postgresql.Driver"
2025-08-26 13:50:32.184 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName................................none
2025-08-26 13:50:32.185 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties................................{}
2025-08-26 13:50:32.185 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry................................none
2025-08-26 13:50:32.185 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout................................***********-08-26 13:50:32.185 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout................................1
2025-08-26 13:50:32.185 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries................................false
2025-08-26 13:50:32.185 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl................................********************************************
2025-08-26 13:50:32.185 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime................................0
2025-08-26 13:50:32.185 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold................................0
2025-08-26 13:50:32.185 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime................................1800000
2025-08-26 13:50:32.185 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize................................10
2025-08-26 13:50:32.185 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry................................none
2025-08-26 13:50:32.185 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory................................none
2025-08-26 13:50:32.185 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle................................10
2025-08-26 13:50:32.185 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - password................................<masked>
2025-08-26 13:50:32.185 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - poolName................................"HikariPool-1"
2025-08-26 13:50:32.185 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - readOnly................................false
2025-08-26 13:50:32.185 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans................................false
2025-08-26 13:50:32.185 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor................................none
2025-08-26 13:50:32.185 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - schema................................none
2025-08-26 13:50:32.185 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory................................internal
2025-08-26 13:50:32.185 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation................................default
2025-08-26 13:50:32.185 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - username................................"admin"
2025-08-26 13:50:32.185 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout................................5000
2025-08-26 13:50:32.185 [[main]] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-26 13:50:32.295 [[main]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@3b021664
2025-08-26 13:50:32.295 [[main]] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-26 13:50:32.308 [[main]] INFO  o.f.c.i.license.VersionPrinter - Flyway Community Edition 8.0.5 by Redgate
2025-08-26 13:50:32.309 [[main]] INFO  o.f.c.i.d.base.BaseDatabaseType - Database: ******************************************** (PostgreSQL 16.9)
2025-08-26 13:50:32.309 [[main]] DEBUG o.f.c.i.d.base.BaseDatabaseType - Driver  : PostgreSQL JDBC Driver 42.3.1
2025-08-26 13:50:32.310 [[main]] DEBUG org.flywaydb.core.FlywayExecutor - DDL Transactions Supported: true
2025-08-26 13:50:32.310 [[main]] DEBUG o.f.c.i.s.SchemaHistoryFactory - Schemas: 
2025-08-26 13:50:32.310 [[main]] DEBUG o.f.c.i.s.SchemaHistoryFactory - Default schema: null
2025-08-26 13:50:32.313 [[main]] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: PostgreSQL 16.9 is newer than this version of Flyway and support has not been tested. The latest supported version of PostgreSQL is 14.
2025-08-26 13:50:32.314 [[main]] DEBUG o.f.c.i.c.SqlScriptCallbackFactory - Scanning for SQL callbacks ...
2025-08-26 13:50:32.325 [[main]] DEBUG o.f.core.internal.command.DbValidate - Validating migrations ...
2025-08-26 13:50:32.334 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V20__UPDATE_TABLE_USER.sql (filename: V20__UPDATE_TABLE_USER.sql)
2025-08-26 13:50:32.334 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V31__UPDATE_TABLE_LICENSE_FOR_SELLING.sql (filename: V31__UPDATE_TABLE_LICENSE_FOR_SELLING.sql)
2025-08-26 13:50:32.334 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V11__CREATE_TABLE_TRACKINGS.sql (filename: V11__CREATE_TABLE_TRACKINGS.sql)
2025-08-26 13:50:32.334 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V2__CREATE_TABLE_MODEMS.sql (filename: V2__CREATE_TABLE_MODEMS.sql)
2025-08-26 13:50:32.334 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V38__UPDATE_TABLE_MAIL.sql (filename: V38__UPDATE_TABLE_MAIL.sql)
2025-08-26 13:50:32.334 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V25__UPDATE_TABLE_MAIL.sql (filename: V25__UPDATE_TABLE_MAIL.sql)
2025-08-26 13:50:32.334 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V6__CREATE_TABLE_TRANSACTIONS.sql (filename: V6__CREATE_TABLE_TRANSACTIONS.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V18__UPDATE_TABLE_TRANSACTION.sql (filename: V18__UPDATE_TABLE_TRANSACTION.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V12__UPDATE_TABLE_TRANSACTIONS.sql (filename: V12__UPDATE_TABLE_TRANSACTIONS.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V39__UPDATE_TABLE_TRANSACTION.sql (filename: V39__UPDATE_TABLE_TRANSACTION.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V4__CREATE_TABLE_LICENSES.sql (filename: V4__CREATE_TABLE_LICENSES.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V22__UPDATE_TABLE_CONFIG.sql (filename: V22__UPDATE_TABLE_CONFIG.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V14__UPDATE_TABLE_PROXYS.sql (filename: V14__UPDATE_TABLE_PROXYS.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V8__CREATE_TABLE_REFRESH_TOKENS.sql (filename: V8__CREATE_TABLE_REFRESH_TOKENS.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V23__CREATE_TABLE_MAIL_TEMPLATE.sql (filename: V23__CREATE_TABLE_MAIL_TEMPLATE.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V35__UPDATE_TABLE_PACKAGES.sql (filename: V35__UPDATE_TABLE_PACKAGES.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V15__UPDATE_TABLE_TRANSACTION.sql (filename: V15__UPDATE_TABLE_TRANSACTION.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V17__CREATE_TABLE_PROMOTION.sql (filename: V17__CREATE_TABLE_PROMOTION.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V19__UPDATE_TABLE_LICENSES.sql (filename: V19__UPDATE_TABLE_LICENSES.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__CREATE_TABLE_USERS.sql (filename: V1__CREATE_TABLE_USERS.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V34__UPDATE_TABLE_PROXY_FOR_SPEED_TEST.sql (filename: V34__UPDATE_TABLE_PROXY_FOR_SPEED_TEST.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V16__UPDATE_TABLE_CONFIG.sql (filename: V16__UPDATE_TABLE_CONFIG.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V37__UPDATE_TABLE_LICENSES.sql (filename: V37__UPDATE_TABLE_LICENSES.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V10__CREATE_TABLE_LOCATIONS.sql (filename: V10__CREATE_TABLE_LOCATIONS.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V33__UPDATE_TABLE_PROXY_FOR_ISP.sql (filename: V33__UPDATE_TABLE_PROXY_FOR_ISP.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V5__CREATE_TABLE_PACKAGES.sql (filename: V5__CREATE_TABLE_PACKAGES.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V40__UPDATE_TABLE_PROMOTION.sql (filename: V40__UPDATE_TABLE_PROMOTION.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V32__CREATE_TABLE_PROXYS.sql (filename: V32__CREATE_TABLE_PROXYS.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V42__UPDATE_TABLE_PACKAGES_ADD_LOCATION_FEE.sql (filename: V42__UPDATE_TABLE_PACKAGES_ADD_LOCATION_FEE.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V9__CREATE_TABLE_CONFIGURATIONS.sql (filename: V9__CREATE_TABLE_CONFIGURATIONS.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V7__CREATE_TABLE_MONITORS.sql (filename: V7__CREATE_TABLE_MONITORS.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V41__UPDATE_TABLE_LICENSES_AUTO_RENEWAL.sql (filename: V41__UPDATE_TABLE_LICENSES_AUTO_RENEWAL.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V21__UPDATE_TABLE_TRANSACTION.sql (filename: V21__UPDATE_TABLE_TRANSACTION.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V24__UPDATE_TABLE_PROXYS.sql (filename: V24__UPDATE_TABLE_PROXYS.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V13__UPDATE_TABLE_PROXYS.sql (filename: V13__UPDATE_TABLE_PROXYS.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V36__UPDATE_TABLE_LICENSES.sql (filename: V36__UPDATE_TABLE_LICENSES.sql)
2025-08-26 13:50:32.335 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V3__CREATE_TABLE_PROXY_WANS.sql (filename: V3__CREATE_TABLE_PROXY_WANS.sql)
2025-08-26 13:50:32.346 [[main]] INFO  o.f.core.internal.command.DbValidate - Successfully validated 37 migrations (execution time 00:00.021s)
2025-08-26 13:50:32.351 [[main]] INFO  o.f.core.internal.command.DbMigrate - Current version of schema "public": 42
2025-08-26 13:50:32.352 [[main]] INFO  o.f.core.internal.command.DbMigrate - Schema "public" is up to date. No migration necessary.
2025-08-26 13:50:32.355 [[main]] DEBUG org.flywaydb.core.FlywayExecutor - Memory usage: 63 of 136M
2025-08-26 13:50:32.390 [[main]] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-26 13:50:32.395 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=1, active=0, idle=1, waiting=0)
2025-08-26 13:50:32.412 [[main]] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.3.Final
2025-08-26 13:50:32.415 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@5bd21c2
2025-08-26 13:50:32.433 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@461e96fd
2025-08-26 13:50:32.451 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@7bd3daa9
2025-08-26 13:50:32.468 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@49c81250
2025-08-26 13:50:32.478 [[main]] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-26 13:50:32.485 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@76a1a7dd
2025-08-26 13:50:32.501 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@7ff271cb
2025-08-26 13:50:32.517 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@6abe81e9
2025-08-26 13:50:32.519 [[main]] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQLDialect
2025-08-26 13:50:32.532 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@68f293f1
2025-08-26 13:50:32.548 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@13965e7d
2025-08-26 13:50:32.548 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 13:50:32.877 [[main]] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-26 13:50:32.880 [[main]] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-26 13:50:33.337 [[main]] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-26 13:50:33.521 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/v1/rh-websocket/**'] with []
2025-08-26 13:50:33.521 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/index.html'] with []
2025-08-26 13:50:33.521 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/app.js'] with []
2025-08-26 13:50:33.521 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/favicon.ico'] with []
2025-08-26 13:50:33.538 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7b3d3d3d, org.springframework.security.web.context.SecurityContextPersistenceFilter@d9ff1cd, org.springframework.security.web.header.HeaderWriterFilter@3b4cce5a, org.springframework.security.web.authentication.logout.LogoutFilter@2199e1a4, com.neoproxy.pro.config.JwtRequestFilter@18a349cb, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@77c7d3f4, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@54d0d561, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1eff74f2, org.springframework.security.web.session.SessionManagementFilter@3bb186b, org.springframework.security.web.access.ExceptionTranslationFilter@76dc9569, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@8eda949]
2025-08-26 13:50:33.633 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.AuthenticationController:
	{POST [/v1/users/authentication/email]}: authenticateByEmail(EmailAuthenticationRequest)
2025-08-26 13:50:33.635 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.ISPController:
	{GET [/v1/isp/list]}: getLocations()
2025-08-26 13:50:33.643 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.LocationController:
	{POST [/v1/locations/list]}: getLocations()
	{GET [/v1/locations/list]}: getList()
	{GET [/v1/locations/full-list]}: getFullList()
2025-08-26 13:50:33.645 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.PackageController:
	{POST [/v1/packages/list]}: getPackages(PackageQueryRequest)
	{POST [/v1/packages]}: createNewPackage(PackageRequest)
	{PUT [/v1/packages/{uuid}]}: updatePackage(UUID,PackageRequest)
	{DELETE [/v1/packages/{uuid}]}: deletePackage(UUID)
2025-08-26 13:50:33.645 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.PingController:
	{GET [/v1/ping]}: ping()
2025-08-26 13:50:33.646 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.UserController:
	{GET [/v1/users/me]}: getLoggedUser()
	{POST [/v1/users/register]}: registerNewUser(NewUserRequest)
	{PUT [/v1/users/email]}: registerNewUserByEmail(UserEmailUpdateRequest)
	{PUT [/v1/users/reset-password]}: resetPassword(ResetPasswordRequest)
	{POST [/v1/users/change-password]}: changePassword(ResetPasswordRequest)
	{GET [/v1/users/verify/{code}]}: verifyAccount(String)
	{GET [/v1/users/change-reminder]}: changeReminder()
2025-08-26 13:50:33.647 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ConfigurationController:
	{PUT [/v1/admin/configurations/{uuid}]}: updateConfiguration(UUID,ConfigurationRequest)
	{POST [/v1/admin/configurations/list]}: getList(ConfigurationQueryRequest)
	{POST [/v1/admin/configurations/cate]}: getCate(ConfigurationQueryRequest)
	{PUT [/v1/admin/configurations/cate]}: updateListConf(Map)
	{POST [/v1/admin/configurations/send-notify-email]}: sendNotifyEmail(EmailNotifyRequest)
2025-08-26 13:50:33.648 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.CustomerController:
	{POST [/v1/admin/customers/{uuid}/active]}: active(UUID)
	{POST [/v1/admin/customers/list]}: getList(CustomerQueryRequest)
	{POST [/v1/admin/customers/{uuid}/topup/{amount}]}: topup(UUID,BigDecimal)
	{POST [/v1/admin/customers/{uuid}/refund/{amount}]}: refund(UUID,BigDecimal)
	{POST [/v1/admin/customers/{uuid}/suspended]}: suspended(UUID)
	{ [/v1/admin/customers/excel]}: excel(HttpServletResponse)
	{POST [/v1/admin/customers/{uuid}/reset-password/{new-password}]}: resetPassword(UUID,String)
	{GET [/v1/admin/customers/{uuid}]}: resetPassword(UUID)
2025-08-26 13:50:33.649 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.LicenseController:
	{ [/v1/admin/licenses/tracking]}: excel(HttpServletResponse,UUID)
	{POST [/v1/admin/licenses/list]}: getLicenses(LicenseQueryRequest,HttpServletRequest)
	{PUT [/v1/admin/licenses/{uuid}]}: updateLicense(UUID,LicenseRequest)
	{POST [/v1/admin/licenses/switch-modem]}: switchNewModem(SwitchModemRequest)
	{POST [/v1/admin/licenses/update-license]}: updateLicenseStatus(UpdateLicenseStatusRequest)
	{POST [/v1/admin/licenses/change-proxy]}: changeAvailablePort(LicenseChangeProxyRequest)
2025-08-26 13:50:33.650 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.MailTemplateController:
	{POST [/v1/admin/mail-templates/list]}: getList(MailTemplateQueryRequest)
	{POST [/v1/admin/mail-templates/send-notify-email]}: sendNotifyEmail(EmailNotifyRequest)
	{PUT [/v1/admin/mail-templates/{uuid}]}: updateMailTemplate(UUID,MailTemplateRequest)
	{POST [/v1/admin/mail-templates]}: insertMailTemplate(MailTemplateRequest)
2025-08-26 13:50:33.651 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ModemController:
	{DELETE [/v1/admin/modems/{uuid}]}: delete(UUID)
	{GET [/v1/admin/modems/{uuid}/resume]}: resume(UUID)
	{GET [/v1/admin/modems/{uuid}/sync]}: sync(UUID)
	{POST [/v1/admin/modems]}: createNewModem(ModemRequest)
	{PUT [/v1/admin/modems/{uuid}]}: updateModem(UUID,ModemRequest)
	{POST [/v1/admin/modems/list]}: getModems(ModemQueryRequest)
	{GET [/v1/admin/modems/{uuid}/pause]}: pause(UUID)
	{POST [/v1/admin/modems/generate-port]}: generatePort(GeneratePortRequest)
	{GET [/v1/admin/modems/{uuid}]}: detail(UUID)
2025-08-26 13:50:33.651 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.MonitorController:
	{POST [/v1/admin/monitors/list]}: getList(MonitorQueryRequest)
2025-08-26 13:50:33.651 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.OverviewController:
	{GET [/v1/admin/overview]}: overview()
2025-08-26 13:50:33.651 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.PromotionController:
	{POST [/v1/promotions]}: createNewPromotion(PromotionRequest)
	{PUT [/v1/promotions/{uuid}]}: updatePromotion(UUID,PromotionRequest)
	{POST [/v1/promotions/list]}: getPromotions(PromotionQueryRequest)
	{DELETE [/v1/promotions/{uuid}]}: deletePromotion(UUID)
2025-08-26 13:50:33.651 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ProxyController:
	{POST [/v1/admin/proxies/list]}: getProxyWans(ProxyQueryRequest)
	{DELETE [/v1/admin/proxies/delete]}: deleteProxies(ProxyRequest)
2025-08-26 13:50:33.652 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.TransactionController:
	{POST [/v1/admin/transactions/list]}: getList(TransactionQueryRequest)
2025-08-26 13:50:33.652 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ApiController:
	{GET [/v1/api/proxy/status]}: getStatus(String)
	{GET [/v1/api/proxy/change-ip]}: changeIp(String)
	{GET [/v1/api/proxy/change-rotation-time]}: changeRotationTime(String,Integer)
2025-08-26 13:50:33.653 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientLicenseController:
	{ [/v1/client/licenses/excel]}: excel(HttpServletResponse,String)
	{POST [/v1/client/licenses/change-rotation-time]}: changeRotationTime(ChangeRotationTimeRequest)
	{POST [/v1/client/licenses/list]}: getClientLicenses(LicenseQueryRequest)
	{POST [/v1/client/licenses/extend]}: renewIp(ExtendLicenseRequest)
	{POST [/v1/client/licenses/express-extend]}: expressExtend(ExpressExtendLicenseRequest)
	{POST [/v1/client/licenses/toggle-auto-renewal]}: toggleAutoRenewal(ToggleAutoRenewalRequest)
	{POST [/v1/client/licenses/purchase-vpn]}: purchaseVpn(PurchaseVpnRequest)
	{POST [/v1/client/licenses/cancel-vpn]}: cancelVpn(CancelVpnRequest)
	{POST [/v1/client/licenses/update-tcp-os]}: updateTcpOS(UpdateTcpOSRequest)
2025-08-26 13:50:33.653 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientPackageController:
	{POST [/v1/client/packages/list]}: getPackages(PackageQueryRequest)
2025-08-26 13:50:33.654 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientPromotionController:
	{GET [/v1/client/promotions/redeem-code]}: redeemCode(String,BigDecimal)
	{POST [/v1/client/promotions/quantity]}: getPromotionQuantity(PromotionQueryRequest)
	{GET [/v1/client/promotions/discount]}: getDiscountByPromotion(String,BigDecimal)
2025-08-26 13:50:33.654 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientProxyController:
	{POST [/v1/client/proxies/list]}: getProxyWans(ProxyQueryRequest)
	{POST [/v1/client/proxies/extend-license]}: extendLicense(ExtendLicenseRequest)
	{POST [/v1/client/proxies/change-ip]}: changeProxyIp(ProxyRequest)
	{POST [/v1/client/proxies/reboot-device]}: rebootDevice(ProxyRequest)
	{POST [/v1/client/proxies/change-location]}: changeLocation(ProxyRequest)
	{POST [/v1/client/proxies/update-authenticate]}: updateAuthenticate(ProxyRequest)
2025-08-26 13:50:33.654 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientTransactionController:
	{POST [/v1/client/transactions/list]}: getList(TransactionQueryRequest)
	{POST [/v1/client/transactions/place-order]}: createNewOrder(OrderProxyRequest)
2025-08-26 13:50:33.655 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientTransactionV2Controller:
	{POST [/v1/client/orders/place-order]}: checkOrder(OrderProxyV2Request)
2025-08-26 13:50:33.655 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.PaymentController:
	{POST [/v1/client/payments/topup]}: topup(TopupRequest)
	{GET [/v1/client/payments/currencies]}: getCurrencies()
	{GET [/v1/client/payments/full-currencies]}: getFullCurrencies()
	{GET [/v1/client/payments/minimum-amount]}: getMinimumAmount(String)
	{GET [/v1/client/payments/estimated-price]}: getEstimatedPrice(String,String,BigDecimal)
	{GET [/v1/client/payments/get-payment-url]}: getPaymentUrl(BigDecimal)
	{POST [/v1/client/payments/webhook]}: webhook(String,String)
2025-08-26 13:50:33.655 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.VpnController:
	{POST [/v1/client/vpn/change]}: change(VpnRequest)
	{GET [/v1/client/vpn/download]}: downloadFile(String)
2025-08-26 13:50:33.657 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error]}: error(HttpServletRequest)
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
2025-08-26 13:50:33.664 [[main]] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-08-26 13:50:33.673 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 'beanNameHandlerMapping' {}
2025-08-26 13:50:33.683 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 'resourceHandlerMapping' {/webjars/**=ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]], /**=ResourceHttpRequestHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/], ServletContext [/]]}
2025-08-26 13:50:33.812 [[main]] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 6 endpoint(s) beneath base path '/actuator'
2025-08-26 13:50:33.834 [[main]] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-26 13:50:33.839 [[main]] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/neoproxy'
2025-08-26 13:50:33.849 [[main]] INFO  c.n.pro.NeoProxyApiApplication - Started NeoProxyApiApplication in 2.726 seconds (JVM running for 3.036)
2025-08-26 13:51:02.396 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 13:51:02.396 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 13:51:32.396 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 13:51:32.397 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 13:52:02.397 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 13:52:02.397 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 13:52:32.398 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 13:52:32.398 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 13:53:02.399 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 13:53:02.399 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 13:53:27.197 [[SpringApplicationShutdownHook]] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-26 13:53:27.198 [[SpringApplicationShutdownHook]] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-26 13:53:27.198 [[SpringApplicationShutdownHook]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Before shutdown stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 13:53:27.199 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@3b021664: (connection evicted)
2025-08-26 13:53:27.200 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@5bd21c2: (connection evicted)
2025-08-26 13:53:27.200 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@461e96fd: (connection evicted)
2025-08-26 13:53:27.201 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@7bd3daa9: (connection evicted)
2025-08-26 13:53:27.201 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@49c81250: (connection evicted)
2025-08-26 13:53:27.202 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@76a1a7dd: (connection evicted)
2025-08-26 13:53:27.202 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@7ff271cb: (connection evicted)
2025-08-26 13:53:27.203 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@6abe81e9: (connection evicted)
2025-08-26 13:53:27.203 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@68f293f1: (connection evicted)
2025-08-26 13:53:27.203 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@13965e7d: (connection evicted)
2025-08-26 13:53:27.203 [[SpringApplicationShutdownHook]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After shutdown stats (total=0, active=0, idle=0, waiting=0)
2025-08-26 13:53:27.203 [[SpringApplicationShutdownHook]] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-26 14:00:06.364 [[background-preinit]] DEBUG org.jboss.logging - Logging Provider: org.jboss.logging.Log4j2LoggerProvider
2025-08-26 14:00:06.365 [[background-preinit]] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final
2025-08-26 14:00:06.368 [[main]] INFO  c.n.pro.NeoProxyApiApplication - Starting NeoProxyApiApplication using Java 17.0.16 on vinhbui-HP-EliteBook-845-14-inch-G10-Notebook-PC with PID 32168 (/home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes started by vinhbui8 in /home/<USER>/Documents/aaaproxy/neo-proxy_app)
2025-08-26 14:00:06.368 [[main]] INFO  c.n.pro.NeoProxyApiApplication - The following profiles are active: dev
2025-08-26 14:00:06.782 [[main]] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-26 14:00:06.830 [[main]] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 45 ms. Found 14 JPA repository interfaces.
2025-08-26 14:00:07.085 [[main]] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-26 14:00:07.089 [[main]] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-26 14:00:07.090 [[main]] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-26 14:00:07.090 [[main]] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.56]
2025-08-26 14:00:07.126 [[main]] INFO  o.a.c.c.C.[.[localhost].[/neoproxy] - Initializing Spring embedded WebApplicationContext
2025-08-26 14:00:07.126 [[main]] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 739 ms
2025-08-26 14:00:07.154 [[main]] DEBUG i.m.c.u.i.l.InternalLoggerFactory - Using SLF4J as the default logging framework
2025-08-26 14:00:07.229 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - Driver class org.postgresql.Driver found in Thread context class loader TomcatEmbeddedWebappClassLoader
  context: neoproxy
  delegate: true
----------> Parent Classloader:
jdk.internal.loader.ClassLoaders$AppClassLoader@30946e09

2025-08-26 14:00:07.258 [[main]] DEBUG o.f.c.internal.util.FeatureDetector - AWS SDK available: false
2025-08-26 14:00:07.258 [[main]] DEBUG o.f.c.internal.util.FeatureDetector - Google Cloud Storage available: false
2025-08-26 14:00:07.258 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/callback' ...
2025-08-26 14:00:07.258 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/callback using ClassLoader TomcatEmbeddedWebappClassLoader
  context: neoproxy
  delegate: true
----------> Parent Classloader:
jdk.internal.loader.ClassLoaders$AppClassLoader@30946e09
 ...
2025-08-26 14:00:07.258 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Unable to resolve location classpath:db/callback.
2025-08-26 14:00:07.258 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/migration' ...
2025-08-26 14:00:07.258 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/migration using ClassLoader TomcatEmbeddedWebappClassLoader
  context: neoproxy
  delegate: true
----------> Parent Classloader:
jdk.internal.loader.ClassLoaders$AppClassLoader@30946e09
 ...
2025-08-26 14:00:07.259 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning URL: file:/home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes/db/migration
2025-08-26 14:00:07.259 [[main]] DEBUG o.f.c.internal.util.FeatureDetector - JBoss VFS v2 available: false
2025-08-26 14:00:07.259 [[main]] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning starting at classpath root in filesystem: /home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes/
2025-08-26 14:00:07.259 [[main]] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning for resources in path: /home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes/db/migration (db/migration)
2025-08-26 14:00:07.260 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V10__CREATE_TABLE_LOCATIONS.sql
2025-08-26 14:00:07.260 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V11__CREATE_TABLE_TRACKINGS.sql
2025-08-26 14:00:07.260 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V12__UPDATE_TABLE_TRANSACTIONS.sql
2025-08-26 14:00:07.260 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V13__UPDATE_TABLE_PROXYS.sql
2025-08-26 14:00:07.260 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V14__UPDATE_TABLE_PROXYS.sql
2025-08-26 14:00:07.260 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V15__UPDATE_TABLE_TRANSACTION.sql
2025-08-26 14:00:07.260 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V16__UPDATE_TABLE_CONFIG.sql
2025-08-26 14:00:07.260 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V17__CREATE_TABLE_PROMOTION.sql
2025-08-26 14:00:07.260 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V18__UPDATE_TABLE_TRANSACTION.sql
2025-08-26 14:00:07.260 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V19__UPDATE_TABLE_LICENSES.sql
2025-08-26 14:00:07.260 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V1__CREATE_TABLE_USERS.sql
2025-08-26 14:00:07.260 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V20__UPDATE_TABLE_USER.sql
2025-08-26 14:00:07.260 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V21__UPDATE_TABLE_TRANSACTION.sql
2025-08-26 14:00:07.260 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V22__UPDATE_TABLE_CONFIG.sql
2025-08-26 14:00:07.260 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V23__CREATE_TABLE_MAIL_TEMPLATE.sql
2025-08-26 14:00:07.260 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V24__UPDATE_TABLE_PROXYS.sql
2025-08-26 14:00:07.260 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V25__UPDATE_TABLE_MAIL.sql
2025-08-26 14:00:07.260 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V2__CREATE_TABLE_MODEMS.sql
2025-08-26 14:00:07.260 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V31__UPDATE_TABLE_LICENSE_FOR_SELLING.sql
2025-08-26 14:00:07.260 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V32__CREATE_TABLE_PROXYS.sql
2025-08-26 14:00:07.260 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V33__UPDATE_TABLE_PROXY_FOR_ISP.sql
2025-08-26 14:00:07.260 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V34__UPDATE_TABLE_PROXY_FOR_SPEED_TEST.sql
2025-08-26 14:00:07.260 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V35__UPDATE_TABLE_PACKAGES.sql
2025-08-26 14:00:07.260 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V36__UPDATE_TABLE_LICENSES.sql
2025-08-26 14:00:07.260 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V37__UPDATE_TABLE_LICENSES.sql
2025-08-26 14:00:07.261 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V38__UPDATE_TABLE_MAIL.sql
2025-08-26 14:00:07.261 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V39__UPDATE_TABLE_TRANSACTION.sql
2025-08-26 14:00:07.261 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V3__CREATE_TABLE_PROXY_WANS.sql
2025-08-26 14:00:07.261 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V40__UPDATE_TABLE_PROMOTION.sql
2025-08-26 14:00:07.261 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V41__UPDATE_TABLE_LICENSES_AUTO_RENEWAL.sql
2025-08-26 14:00:07.261 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V42__UPDATE_TABLE_PACKAGES_ADD_LOCATION_FEE.sql
2025-08-26 14:00:07.261 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V4__CREATE_TABLE_LICENSES.sql
2025-08-26 14:00:07.261 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V5__CREATE_TABLE_PACKAGES.sql
2025-08-26 14:00:07.261 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V6__CREATE_TABLE_TRANSACTIONS.sql
2025-08-26 14:00:07.261 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V7__CREATE_TABLE_MONITORS.sql
2025-08-26 14:00:07.261 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V8__CREATE_TABLE_REFRESH_TOKENS.sql
2025-08-26 14:00:07.261 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V9__CREATE_TABLE_CONFIGURATIONS.sql
2025-08-26 14:00:07.261 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classes at classpath:db/migration
2025-08-26 14:00:07.262 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-08-26 14:00:07.262 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension................................false
2025-08-26 14:00:07.262 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit................................true
2025-08-26 14:00:07.262 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - catalog................................none
2025-08-26 14:00:07.262 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql................................none
2025-08-26 14:00:07.262 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery................................none
2025-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout................................30000
2025-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSource................................none
2025-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName................................none
2025-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI................................none
2025-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties................................{password=<masked>}
2025-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................................"org.postgresql.Driver"
2025-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName................................none
2025-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties................................{}
2025-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry................................none
2025-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout................................***********-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout................................1
2025-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries................................false
2025-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl................................**************************************************************************************
2025-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime................................0
2025-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold................................0
2025-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime................................1800000
2025-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize................................10
2025-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry................................none
2025-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory................................none
2025-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle................................10
2025-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - password................................<masked>
2025-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - poolName................................"HikariPool-1"
2025-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - readOnly................................false
2025-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans................................false
2025-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor................................none
2025-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - schema................................none
2025-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory................................internal
2025-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation................................default
2025-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - username................................"proxydb"
2025-08-26 14:00:07.263 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout................................5000
2025-08-26 14:00:07.264 [[main]] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-26 14:00:10.924 [[main]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@15c16f19
2025-08-26 14:00:10.925 [[main]] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-26 14:00:11.026 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=1, active=1, idle=0, waiting=0)
2025-08-26 14:00:11.544 [[main]] INFO  o.f.c.i.license.VersionPrinter - Flyway Community Edition 8.0.5 by Redgate
2025-08-26 14:00:11.544 [[main]] INFO  o.f.c.i.d.base.BaseDatabaseType - Database: ************************************************************************************** (PostgreSQL 13.15)
2025-08-26 14:00:11.544 [[main]] DEBUG o.f.c.i.d.base.BaseDatabaseType - Driver  : PostgreSQL JDBC Driver 42.3.1
2025-08-26 14:00:11.545 [[main]] DEBUG org.flywaydb.core.FlywayExecutor - DDL Transactions Supported: true
2025-08-26 14:00:11.546 [[main]] DEBUG o.f.c.i.s.SchemaHistoryFactory - Schemas: 
2025-08-26 14:00:11.546 [[main]] DEBUG o.f.c.i.s.SchemaHistoryFactory - Default schema: null
2025-08-26 14:00:13.796 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@50ca141a
2025-08-26 14:00:14.051 [[main]] DEBUG o.f.c.i.c.SqlScriptCallbackFactory - Scanning for SQL callbacks ...
2025-08-26 14:00:14.402 [[main]] DEBUG o.f.core.internal.command.DbValidate - Validating migrations ...
2025-08-26 14:00:14.414 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V20__UPDATE_TABLE_USER.sql (filename: V20__UPDATE_TABLE_USER.sql)
2025-08-26 14:00:14.414 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V31__UPDATE_TABLE_LICENSE_FOR_SELLING.sql (filename: V31__UPDATE_TABLE_LICENSE_FOR_SELLING.sql)
2025-08-26 14:00:14.414 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V11__CREATE_TABLE_TRACKINGS.sql (filename: V11__CREATE_TABLE_TRACKINGS.sql)
2025-08-26 14:00:14.414 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V2__CREATE_TABLE_MODEMS.sql (filename: V2__CREATE_TABLE_MODEMS.sql)
2025-08-26 14:00:14.414 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V38__UPDATE_TABLE_MAIL.sql (filename: V38__UPDATE_TABLE_MAIL.sql)
2025-08-26 14:00:14.414 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V25__UPDATE_TABLE_MAIL.sql (filename: V25__UPDATE_TABLE_MAIL.sql)
2025-08-26 14:00:14.414 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V6__CREATE_TABLE_TRANSACTIONS.sql (filename: V6__CREATE_TABLE_TRANSACTIONS.sql)
2025-08-26 14:00:14.414 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V18__UPDATE_TABLE_TRANSACTION.sql (filename: V18__UPDATE_TABLE_TRANSACTION.sql)
2025-08-26 14:00:14.414 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V12__UPDATE_TABLE_TRANSACTIONS.sql (filename: V12__UPDATE_TABLE_TRANSACTIONS.sql)
2025-08-26 14:00:14.414 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V39__UPDATE_TABLE_TRANSACTION.sql (filename: V39__UPDATE_TABLE_TRANSACTION.sql)
2025-08-26 14:00:14.414 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V4__CREATE_TABLE_LICENSES.sql (filename: V4__CREATE_TABLE_LICENSES.sql)
2025-08-26 14:00:14.414 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V22__UPDATE_TABLE_CONFIG.sql (filename: V22__UPDATE_TABLE_CONFIG.sql)
2025-08-26 14:00:14.414 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V14__UPDATE_TABLE_PROXYS.sql (filename: V14__UPDATE_TABLE_PROXYS.sql)
2025-08-26 14:00:14.414 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V8__CREATE_TABLE_REFRESH_TOKENS.sql (filename: V8__CREATE_TABLE_REFRESH_TOKENS.sql)
2025-08-26 14:00:14.414 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V23__CREATE_TABLE_MAIL_TEMPLATE.sql (filename: V23__CREATE_TABLE_MAIL_TEMPLATE.sql)
2025-08-26 14:00:14.414 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V35__UPDATE_TABLE_PACKAGES.sql (filename: V35__UPDATE_TABLE_PACKAGES.sql)
2025-08-26 14:00:14.414 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V15__UPDATE_TABLE_TRANSACTION.sql (filename: V15__UPDATE_TABLE_TRANSACTION.sql)
2025-08-26 14:00:14.414 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V17__CREATE_TABLE_PROMOTION.sql (filename: V17__CREATE_TABLE_PROMOTION.sql)
2025-08-26 14:00:14.414 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V19__UPDATE_TABLE_LICENSES.sql (filename: V19__UPDATE_TABLE_LICENSES.sql)
2025-08-26 14:00:14.414 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__CREATE_TABLE_USERS.sql (filename: V1__CREATE_TABLE_USERS.sql)
2025-08-26 14:00:14.414 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V34__UPDATE_TABLE_PROXY_FOR_SPEED_TEST.sql (filename: V34__UPDATE_TABLE_PROXY_FOR_SPEED_TEST.sql)
2025-08-26 14:00:14.414 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V16__UPDATE_TABLE_CONFIG.sql (filename: V16__UPDATE_TABLE_CONFIG.sql)
2025-08-26 14:00:14.414 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V37__UPDATE_TABLE_LICENSES.sql (filename: V37__UPDATE_TABLE_LICENSES.sql)
2025-08-26 14:00:14.415 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V10__CREATE_TABLE_LOCATIONS.sql (filename: V10__CREATE_TABLE_LOCATIONS.sql)
2025-08-26 14:00:14.415 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V33__UPDATE_TABLE_PROXY_FOR_ISP.sql (filename: V33__UPDATE_TABLE_PROXY_FOR_ISP.sql)
2025-08-26 14:00:14.415 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V5__CREATE_TABLE_PACKAGES.sql (filename: V5__CREATE_TABLE_PACKAGES.sql)
2025-08-26 14:00:14.415 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V40__UPDATE_TABLE_PROMOTION.sql (filename: V40__UPDATE_TABLE_PROMOTION.sql)
2025-08-26 14:00:14.415 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V32__CREATE_TABLE_PROXYS.sql (filename: V32__CREATE_TABLE_PROXYS.sql)
2025-08-26 14:00:14.415 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V42__UPDATE_TABLE_PACKAGES_ADD_LOCATION_FEE.sql (filename: V42__UPDATE_TABLE_PACKAGES_ADD_LOCATION_FEE.sql)
2025-08-26 14:00:14.415 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V9__CREATE_TABLE_CONFIGURATIONS.sql (filename: V9__CREATE_TABLE_CONFIGURATIONS.sql)
2025-08-26 14:00:14.415 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V7__CREATE_TABLE_MONITORS.sql (filename: V7__CREATE_TABLE_MONITORS.sql)
2025-08-26 14:00:14.415 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V41__UPDATE_TABLE_LICENSES_AUTO_RENEWAL.sql (filename: V41__UPDATE_TABLE_LICENSES_AUTO_RENEWAL.sql)
2025-08-26 14:00:14.415 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V21__UPDATE_TABLE_TRANSACTION.sql (filename: V21__UPDATE_TABLE_TRANSACTION.sql)
2025-08-26 14:00:14.415 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V24__UPDATE_TABLE_PROXYS.sql (filename: V24__UPDATE_TABLE_PROXYS.sql)
2025-08-26 14:00:14.415 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V13__UPDATE_TABLE_PROXYS.sql (filename: V13__UPDATE_TABLE_PROXYS.sql)
2025-08-26 14:00:14.415 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V36__UPDATE_TABLE_LICENSES.sql (filename: V36__UPDATE_TABLE_LICENSES.sql)
2025-08-26 14:00:14.415 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V3__CREATE_TABLE_PROXY_WANS.sql (filename: V3__CREATE_TABLE_PROXY_WANS.sql)
2025-08-26 14:00:15.544 [[main]] INFO  o.f.core.internal.command.DbValidate - Successfully validated 37 migrations (execution time 00:01.140s)
2025-08-26 14:00:16.640 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2e65a290
2025-08-26 14:00:17.518 [[main]] INFO  o.f.core.internal.command.DbMigrate - Current version of schema "public": 42
2025-08-26 14:00:17.772 [[main]] INFO  o.f.core.internal.command.DbMigrate - Schema "public" is up to date. No migration necessary.
2025-08-26 14:00:18.621 [[main]] DEBUG org.flywaydb.core.FlywayExecutor - Memory usage: 63 of 136M
2025-08-26 14:00:18.658 [[main]] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-26 14:00:18.678 [[main]] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.3.Final
2025-08-26 14:00:18.742 [[main]] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-26 14:00:18.785 [[main]] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQLDialect
2025-08-26 14:00:19.424 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@67655119
2025-08-26 14:00:19.695 [[main]] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-26 14:00:19.699 [[main]] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-26 14:00:20.216 [[main]] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-26 14:00:20.396 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/v1/rh-websocket/**'] with []
2025-08-26 14:00:20.396 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/index.html'] with []
2025-08-26 14:00:20.396 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/app.js'] with []
2025-08-26 14:00:20.396 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/favicon.ico'] with []
2025-08-26 14:00:20.411 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@26864891, org.springframework.security.web.context.SecurityContextPersistenceFilter@5d04adc1, org.springframework.security.web.header.HeaderWriterFilter@56d189d0, org.springframework.security.web.authentication.logout.LogoutFilter@71cdde0c, com.neoproxy.pro.config.JwtRequestFilter@60147536, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@346e76ad, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@555454c7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5946e540, org.springframework.security.web.session.SessionManagementFilter@7ee0dbd1, org.springframework.security.web.access.ExceptionTranslationFilter@25d6ae3, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@22cbbe87]
2025-08-26 14:00:20.500 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.AuthenticationController:
	{POST [/v1/users/authentication/email]}: authenticateByEmail(EmailAuthenticationRequest)
2025-08-26 14:00:20.502 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.ISPController:
	{GET [/v1/isp/list]}: getLocations()
2025-08-26 14:00:20.503 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.LocationController:
	{POST [/v1/locations/list]}: getLocations()
	{GET [/v1/locations/list]}: getList()
	{GET [/v1/locations/full-list]}: getFullList()
2025-08-26 14:00:20.504 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.PackageController:
	{POST [/v1/packages/list]}: getPackages(PackageQueryRequest)
	{POST [/v1/packages]}: createNewPackage(PackageRequest)
	{PUT [/v1/packages/{uuid}]}: updatePackage(UUID,PackageRequest)
	{DELETE [/v1/packages/{uuid}]}: deletePackage(UUID)
2025-08-26 14:00:20.504 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.PingController:
	{GET [/v1/ping]}: ping()
2025-08-26 14:00:20.505 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.UserController:
	{GET [/v1/users/me]}: getLoggedUser()
	{POST [/v1/users/register]}: registerNewUser(NewUserRequest)
	{PUT [/v1/users/email]}: registerNewUserByEmail(UserEmailUpdateRequest)
	{PUT [/v1/users/reset-password]}: resetPassword(ResetPasswordRequest)
	{POST [/v1/users/change-password]}: changePassword(ResetPasswordRequest)
	{GET [/v1/users/verify/{code}]}: verifyAccount(String)
	{GET [/v1/users/change-reminder]}: changeReminder()
2025-08-26 14:00:20.506 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ConfigurationController:
	{PUT [/v1/admin/configurations/{uuid}]}: updateConfiguration(UUID,ConfigurationRequest)
	{POST [/v1/admin/configurations/list]}: getList(ConfigurationQueryRequest)
	{POST [/v1/admin/configurations/cate]}: getCate(ConfigurationQueryRequest)
	{PUT [/v1/admin/configurations/cate]}: updateListConf(Map)
	{POST [/v1/admin/configurations/send-notify-email]}: sendNotifyEmail(EmailNotifyRequest)
2025-08-26 14:00:20.507 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.CustomerController:
	{POST [/v1/admin/customers/{uuid}/active]}: active(UUID)
	{POST [/v1/admin/customers/list]}: getList(CustomerQueryRequest)
	{POST [/v1/admin/customers/{uuid}/topup/{amount}]}: topup(UUID,BigDecimal)
	{POST [/v1/admin/customers/{uuid}/refund/{amount}]}: refund(UUID,BigDecimal)
	{POST [/v1/admin/customers/{uuid}/suspended]}: suspended(UUID)
	{ [/v1/admin/customers/excel]}: excel(HttpServletResponse)
	{POST [/v1/admin/customers/{uuid}/reset-password/{new-password}]}: resetPassword(UUID,String)
	{GET [/v1/admin/customers/{uuid}]}: resetPassword(UUID)
2025-08-26 14:00:20.508 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.LicenseController:
	{ [/v1/admin/licenses/tracking]}: excel(HttpServletResponse,UUID)
	{POST [/v1/admin/licenses/list]}: getLicenses(LicenseQueryRequest,HttpServletRequest)
	{PUT [/v1/admin/licenses/{uuid}]}: updateLicense(UUID,LicenseRequest)
	{POST [/v1/admin/licenses/switch-modem]}: switchNewModem(SwitchModemRequest)
	{POST [/v1/admin/licenses/update-license]}: updateLicenseStatus(UpdateLicenseStatusRequest)
	{POST [/v1/admin/licenses/change-proxy]}: changeAvailablePort(LicenseChangeProxyRequest)
2025-08-26 14:00:20.508 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.MailTemplateController:
	{POST [/v1/admin/mail-templates/list]}: getList(MailTemplateQueryRequest)
	{POST [/v1/admin/mail-templates/send-notify-email]}: sendNotifyEmail(EmailNotifyRequest)
	{PUT [/v1/admin/mail-templates/{uuid}]}: updateMailTemplate(UUID,MailTemplateRequest)
	{POST [/v1/admin/mail-templates]}: insertMailTemplate(MailTemplateRequest)
2025-08-26 14:00:20.509 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ModemController:
	{DELETE [/v1/admin/modems/{uuid}]}: delete(UUID)
	{GET [/v1/admin/modems/{uuid}/resume]}: resume(UUID)
	{GET [/v1/admin/modems/{uuid}/sync]}: sync(UUID)
	{GET [/v1/admin/modems/{uuid}]}: detail(UUID)
	{POST [/v1/admin/modems]}: createNewModem(ModemRequest)
	{PUT [/v1/admin/modems/{uuid}]}: updateModem(UUID,ModemRequest)
	{POST [/v1/admin/modems/list]}: getModems(ModemQueryRequest)
	{GET [/v1/admin/modems/{uuid}/pause]}: pause(UUID)
	{POST [/v1/admin/modems/generate-port]}: generatePort(GeneratePortRequest)
2025-08-26 14:00:20.510 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.MonitorController:
	{POST [/v1/admin/monitors/list]}: getList(MonitorQueryRequest)
2025-08-26 14:00:20.510 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.OverviewController:
	{GET [/v1/admin/overview]}: overview()
2025-08-26 14:00:20.510 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.PromotionController:
	{POST [/v1/promotions]}: createNewPromotion(PromotionRequest)
	{PUT [/v1/promotions/{uuid}]}: updatePromotion(UUID,PromotionRequest)
	{POST [/v1/promotions/list]}: getPromotions(PromotionQueryRequest)
	{DELETE [/v1/promotions/{uuid}]}: deletePromotion(UUID)
2025-08-26 14:00:20.510 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ProxyController:
	{POST [/v1/admin/proxies/list]}: getProxyWans(ProxyQueryRequest)
	{DELETE [/v1/admin/proxies/delete]}: deleteProxies(ProxyRequest)
2025-08-26 14:00:20.510 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.TransactionController:
	{POST [/v1/admin/transactions/list]}: getList(TransactionQueryRequest)
2025-08-26 14:00:20.511 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ApiController:
	{GET [/v1/api/proxy/status]}: getStatus(String)
	{GET [/v1/api/proxy/change-ip]}: changeIp(String)
	{GET [/v1/api/proxy/change-rotation-time]}: changeRotationTime(String,Integer)
2025-08-26 14:00:20.512 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientLicenseController:
	{ [/v1/client/licenses/excel]}: excel(HttpServletResponse,String)
	{POST [/v1/client/licenses/change-rotation-time]}: changeRotationTime(ChangeRotationTimeRequest)
	{POST [/v1/client/licenses/list]}: getClientLicenses(LicenseQueryRequest)
	{POST [/v1/client/licenses/extend]}: renewIp(ExtendLicenseRequest)
	{POST [/v1/client/licenses/express-extend]}: expressExtend(ExpressExtendLicenseRequest)
	{POST [/v1/client/licenses/toggle-auto-renewal]}: toggleAutoRenewal(ToggleAutoRenewalRequest)
	{POST [/v1/client/licenses/purchase-vpn]}: purchaseVpn(PurchaseVpnRequest)
	{POST [/v1/client/licenses/cancel-vpn]}: cancelVpn(CancelVpnRequest)
	{POST [/v1/client/licenses/update-tcp-os]}: updateTcpOS(UpdateTcpOSRequest)
2025-08-26 14:00:20.512 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientPackageController:
	{POST [/v1/client/packages/list]}: getPackages(PackageQueryRequest)
2025-08-26 14:00:20.512 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientPromotionController:
	{GET [/v1/client/promotions/redeem-code]}: redeemCode(String,BigDecimal)
	{POST [/v1/client/promotions/quantity]}: getPromotionQuantity(PromotionQueryRequest)
	{GET [/v1/client/promotions/discount]}: getDiscountByPromotion(String,BigDecimal)
2025-08-26 14:00:20.513 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientProxyController:
	{POST [/v1/client/proxies/list]}: getProxyWans(ProxyQueryRequest)
	{POST [/v1/client/proxies/change-ip]}: changeProxyIp(ProxyRequest)
	{POST [/v1/client/proxies/reboot-device]}: rebootDevice(ProxyRequest)
	{POST [/v1/client/proxies/change-location]}: changeLocation(ProxyRequest)
	{POST [/v1/client/proxies/update-authenticate]}: updateAuthenticate(ProxyRequest)
	{POST [/v1/client/proxies/extend-license]}: extendLicense(ExtendLicenseRequest)
2025-08-26 14:00:20.513 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientTransactionController:
	{POST [/v1/client/transactions/list]}: getList(TransactionQueryRequest)
	{POST [/v1/client/transactions/place-order]}: createNewOrder(OrderProxyRequest)
2025-08-26 14:00:20.513 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientTransactionV2Controller:
	{POST [/v1/client/orders/place-order]}: checkOrder(OrderProxyV2Request)
2025-08-26 14:00:20.513 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.PaymentController:
	{POST [/v1/client/payments/topup]}: topup(TopupRequest)
	{GET [/v1/client/payments/currencies]}: getCurrencies()
	{GET [/v1/client/payments/full-currencies]}: getFullCurrencies()
	{GET [/v1/client/payments/minimum-amount]}: getMinimumAmount(String)
	{GET [/v1/client/payments/estimated-price]}: getEstimatedPrice(String,String,BigDecimal)
	{GET [/v1/client/payments/get-payment-url]}: getPaymentUrl(BigDecimal)
	{POST [/v1/client/payments/webhook]}: webhook(String,String)
2025-08-26 14:00:20.514 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.VpnController:
	{POST [/v1/client/vpn/change]}: change(VpnRequest)
	{GET [/v1/client/vpn/download]}: downloadFile(String)
2025-08-26 14:00:20.515 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error]}: error(HttpServletRequest)
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
2025-08-26 14:00:20.521 [[main]] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-08-26 14:00:20.529 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 'beanNameHandlerMapping' {}
2025-08-26 14:00:20.538 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 'resourceHandlerMapping' {/webjars/**=ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]], /**=ResourceHttpRequestHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/], ServletContext [/]]}
2025-08-26 14:00:20.671 [[main]] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 6 endpoint(s) beneath base path '/actuator'
2025-08-26 14:00:20.696 [[main]] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-26 14:00:20.702 [[main]] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/neoproxy'
2025-08-26 14:00:20.712 [[main]] INFO  c.n.pro.NeoProxyApiApplication - Started NeoProxyApiApplication in 14.508 seconds (JVM running for 14.809)
2025-08-26 14:00:22.543 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@31c1e9b7
2025-08-26 14:00:25.501 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@7c619b0f
2025-08-26 14:00:28.348 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@6a9da347
2025-08-26 14:00:31.342 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@42a6c28d
2025-08-26 14:00:34.423 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@45a6c605
2025-08-26 14:00:37.365 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@78e8097d
2025-08-26 14:00:37.365 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:00:41.026 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:00:41.027 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:00:55.788 [[http-nio-8080-exec-1]] INFO  o.a.c.c.C.[.[localhost].[/neoproxy] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-26 14:00:55.788 [[http-nio-8080-exec-1]] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-26 14:00:55.789 [[http-nio-8080-exec-1]] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-26 14:00:57.843 [[http-nio-8080-exec-2]] INFO  c.n.p.s.impl.LicenseServiceImpl - 2025-08-26 - 2025-08-27T00:00
2025-08-26 14:00:57.966 [[http-nio-8080-exec-6]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [https://api-sandbox.nowpayments.io/v1/merchant/coins]
2025-08-26 14:00:57.966 [[http-nio-8080-exec-6]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 14:00:57.966 [[http-nio-8080-exec-6]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [x-api-key: H3VKFJT-8VSM715-QRWG6JH-1MMACJW]
2025-08-26 14:00:59.113 [[http-nio-8080-exec-6]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [403]
2025-08-26 14:00:59.113 [[http-nio-8080-exec-6]] INFO  c.n.pro.utils.httpClient.ApiClient - Error message: [{"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}]
2025-08-26 14:00:59.114 [[http-nio-8080-exec-6]] INFO  c.n.pro.utils.httpClient.ApiClient - Status NOT OK. throw Error code: [400]
2025-08-26 14:00:59.115 [[http-nio-8080-exec-6]] ERROR c.n.pro.utils.httpClient.ApiClient -  CALL PARTNER [Error] https://api-sandbox.nowpayments.io/v1/merchant/coins
java.lang.Exception: 400 Error when map code: 403
	at com.neoproxy.pro.utils.httpClient.ApiClient.invoke(ApiClient.java:91)
	at com.neoproxy.pro.utils.httpClient.ApiClient.callAPI(ApiClient.java:61)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getSelectedCurrencies(NowPaymentServiceImpl.java:58)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getFullCurrencies(NowPaymentServiceImpl.java:72)
	at com.neoproxy.pro.controller.client.PaymentController.getFullCurrencies(PaymentController.java:46)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.neoproxy.pro.config.JwtRequestFilter.doFilterInternal(JwtRequestFilter.java:33)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1732)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-26 14:00:59.116 [[http-nio-8080-exec-6]] ERROR c.n.p.n.s.impl.NowPaymentServiceImpl - [NowPayment] Error: 400
2025-08-26 14:00:59.503 [[http-nio-8080-exec-6]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [https://api-sandbox.nowpayments.io/v1/full-currencies]
2025-08-26 14:00:59.504 [[http-nio-8080-exec-6]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 14:00:59.504 [[http-nio-8080-exec-6]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [x-api-key: H3VKFJT-8VSM715-QRWG6JH-1MMACJW]
2025-08-26 14:00:59.765 [[http-nio-8080-exec-6]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [403]
2025-08-26 14:00:59.765 [[http-nio-8080-exec-6]] INFO  c.n.pro.utils.httpClient.ApiClient - Error message: [{"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}]
2025-08-26 14:00:59.765 [[http-nio-8080-exec-6]] INFO  c.n.pro.utils.httpClient.ApiClient - Status NOT OK. throw Error code: [400]
2025-08-26 14:00:59.765 [[http-nio-8080-exec-6]] ERROR c.n.pro.utils.httpClient.ApiClient -  CALL PARTNER [Error] https://api-sandbox.nowpayments.io/v1/full-currencies
java.lang.Exception: 400 Error when map code: 403
	at com.neoproxy.pro.utils.httpClient.ApiClient.invoke(ApiClient.java:91)
	at com.neoproxy.pro.utils.httpClient.ApiClient.callAPI(ApiClient.java:61)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getFullCurrencies(NowPaymentServiceImpl.java:73)
	at com.neoproxy.pro.controller.client.PaymentController.getFullCurrencies(PaymentController.java:46)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.neoproxy.pro.config.JwtRequestFilter.doFilterInternal(JwtRequestFilter.java:33)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1732)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-26 14:00:59.766 [[http-nio-8080-exec-6]] ERROR c.n.p.n.s.impl.NowPaymentServiceImpl - [NowPayment] Error: 400
2025-08-26 14:01:11.028 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=3, idle=7, waiting=0)
2025-08-26 14:01:11.028 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:01:13.584 [[http-nio-8080-exec-3]] INFO  c.n.p.s.singleton.SpeedTestService - ---LOCATION NEW YORK, NY | ISP T-Mobile | PROXY 1824 | SPEED TEST 26624749
2025-08-26 14:01:15.816 [[http-nio-8080-exec-9]] INFO  c.n.p.s.singleton.SpeedTestService - ---LOCATION NEW YORK, NY | ISP T-Mobile | PROXY 1821 | SPEED TEST 18231288
2025-08-26 14:01:30.646 [[http-nio-8080-exec-3]] INFO  c.n.p.s.singleton.SpeedTestService - ---LOCATION PHILADELPHIA, PA | ISP T-Mobile | PROXY 1772 | SPEED TEST 19113298
2025-08-26 14:01:35.396 [[http-nio-8080-exec-9]] INFO  c.n.p.s.singleton.SpeedTestService - ---LOCATION PHILADELPHIA, PA | ISP T-Mobile | PROXY 1772 | SPEED TEST 19113298
2025-08-26 14:01:41.028 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:01:41.028 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:02:11.029 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:02:11.029 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:02:41.030 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:02:41.030 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:03:11.030 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:03:11.030 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:03:41.030 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:03:41.030 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:04:11.031 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:04:11.031 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:04:41.032 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:04:41.032 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:05:11.032 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:05:11.032 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:05:41.032 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:05:41.033 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:06:11.033 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:06:11.033 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:06:41.033 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:06:41.034 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:07:11.034 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:07:11.036 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:07:41.036 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:07:41.036 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:08:11.037 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:08:11.037 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:08:41.037 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:08:41.037 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:09:11.037 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:09:11.038 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:09:41.038 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:09:41.038 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:10:11.038 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=2, idle=8, waiting=0)
2025-08-26 14:10:11.038 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:10:30.259 [[http-nio-8080-exec-10]] INFO  c.n.p.s.impl.LicenseServiceImpl - 2025-08-26 - 2025-08-27T00:00
2025-08-26 14:10:30.265 [[http-nio-8080-exec-1]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [https://api-sandbox.nowpayments.io/v1/merchant/coins]
2025-08-26 14:10:30.265 [[http-nio-8080-exec-1]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 14:10:30.265 [[http-nio-8080-exec-1]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [x-api-key: H3VKFJT-8VSM715-QRWG6JH-1MMACJW]
2025-08-26 14:10:31.293 [[http-nio-8080-exec-1]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [403]
2025-08-26 14:10:31.293 [[http-nio-8080-exec-1]] INFO  c.n.pro.utils.httpClient.ApiClient - Error message: [{"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}]
2025-08-26 14:10:31.293 [[http-nio-8080-exec-1]] INFO  c.n.pro.utils.httpClient.ApiClient - Status NOT OK. throw Error code: [400]
2025-08-26 14:10:31.293 [[http-nio-8080-exec-1]] ERROR c.n.pro.utils.httpClient.ApiClient -  CALL PARTNER [Error] https://api-sandbox.nowpayments.io/v1/merchant/coins
java.lang.Exception: 400 Error when map code: 403
	at com.neoproxy.pro.utils.httpClient.ApiClient.invoke(ApiClient.java:91)
	at com.neoproxy.pro.utils.httpClient.ApiClient.callAPI(ApiClient.java:61)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getSelectedCurrencies(NowPaymentServiceImpl.java:58)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getFullCurrencies(NowPaymentServiceImpl.java:72)
	at com.neoproxy.pro.controller.client.PaymentController.getFullCurrencies(PaymentController.java:46)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.neoproxy.pro.config.JwtRequestFilter.doFilterInternal(JwtRequestFilter.java:33)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1732)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-26 14:10:31.294 [[http-nio-8080-exec-1]] ERROR c.n.p.n.s.impl.NowPaymentServiceImpl - [NowPayment] Error: 400
2025-08-26 14:10:31.550 [[http-nio-8080-exec-1]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [https://api-sandbox.nowpayments.io/v1/full-currencies]
2025-08-26 14:10:31.550 [[http-nio-8080-exec-1]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 14:10:31.550 [[http-nio-8080-exec-1]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [x-api-key: H3VKFJT-8VSM715-QRWG6JH-1MMACJW]
2025-08-26 14:10:31.789 [[http-nio-8080-exec-1]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [403]
2025-08-26 14:10:31.789 [[http-nio-8080-exec-1]] INFO  c.n.pro.utils.httpClient.ApiClient - Error message: [{"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}]
2025-08-26 14:10:31.789 [[http-nio-8080-exec-1]] INFO  c.n.pro.utils.httpClient.ApiClient - Status NOT OK. throw Error code: [400]
2025-08-26 14:10:31.789 [[http-nio-8080-exec-1]] ERROR c.n.pro.utils.httpClient.ApiClient -  CALL PARTNER [Error] https://api-sandbox.nowpayments.io/v1/full-currencies
java.lang.Exception: 400 Error when map code: 403
	at com.neoproxy.pro.utils.httpClient.ApiClient.invoke(ApiClient.java:91)
	at com.neoproxy.pro.utils.httpClient.ApiClient.callAPI(ApiClient.java:61)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getFullCurrencies(NowPaymentServiceImpl.java:73)
	at com.neoproxy.pro.controller.client.PaymentController.getFullCurrencies(PaymentController.java:46)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.neoproxy.pro.config.JwtRequestFilter.doFilterInternal(JwtRequestFilter.java:33)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1732)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-26 14:10:31.789 [[http-nio-8080-exec-1]] ERROR c.n.p.n.s.impl.NowPaymentServiceImpl - [NowPayment] Error: 400
2025-08-26 14:10:41.038 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:10:41.038 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:10:43.468 [[http-nio-8080-exec-7]] INFO  c.n.p.service.impl.ProxyServiceImpl - Find sale proxy by ISP Location T-Mobile PHILADELPHIA, PA
2025-08-26 14:10:50.541 [[http-nio-8080-exec-7]] INFO  c.n.p.s.i.TransactionV2ServiceImpl - Place order with total amount 10, isp T-Mobile, location PHILADELPHIA, PA
2025-08-26 14:10:50.879 [[http-nio-8080-exec-7]] INFO  c.n.p.service.impl.ProxyServiceImpl - Find sale proxy by ISP Location T-Mobile PHILADELPHIA, PA
2025-08-26 14:10:51.138 [[http-nio-8080-exec-7]] INFO  c.n.p.s.i.TransactionV2ServiceImpl - _____Brother port of http port 32101 with sock port: 37101
2025-08-26 14:10:52.244 [[MyExecutor-2]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - ___ REQUEST: {"positionFrom":1,"positionTo":1,"numberOfPorts":1,"authMethod":0,"authEntry":"","ipAuthenticationEntry":"","userAuthenticationEntry":"uwfkpi8w:p34536","portType":1,"ipType":1,"genPort":2,"genPortStart":30000,"expiredDate":2524582800000,"whitelistLimitAccessEntry":"","blacklistLimitAccessEntry":"","counterUploadLimit":0,"counterUploadLimitBy":1,"counterUploadQuotaInMB":100,"counterDownloadLimit":0,"counterDownloadLimitBy":1,"counterDownloadQuotaInMB":100,"counterAllLimit":0,"counterAllLimitBy":1,"counterAllQuotaInMB":100,"bwLimitEnabled":0,"bwLimitRate":0,"customDNS":"","maxConnection":1000,"memo":"","default_position":true,"default_whitelistLimitAccess":true,"default_blacklistLimitAccess":true,"default_ipAuthentication":false,"default_userAuthentication":false,"default_portType":true,"default_ipType":true,"default_customDNS":true,"default_expiredDate":true,"default_limitAccess":true,"default_bwLimitEnabled":true,"default_counterDownloadLimit":true,"default_counterUploadLimit":true,"default_counterAllLimit":true,"default_maxConnection":false,"default_memo":true,"default_forceServerResolveDns":false,"default_userRotateSetting":false,"ids":[93]}
2025-08-26 14:10:52.244 [[MyExecutor-1]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - ___ REQUEST: {"positionFrom":1,"positionTo":1,"numberOfPorts":1,"authMethod":0,"authEntry":"","ipAuthenticationEntry":"","userAuthenticationEntry":"uwfkpi8w:p34536","portType":1,"ipType":1,"genPort":2,"genPortStart":30000,"expiredDate":2524582800000,"whitelistLimitAccessEntry":"","blacklistLimitAccessEntry":"","counterUploadLimit":0,"counterUploadLimitBy":1,"counterUploadQuotaInMB":100,"counterDownloadLimit":0,"counterDownloadLimitBy":1,"counterDownloadQuotaInMB":100,"counterAllLimit":0,"counterAllLimitBy":1,"counterAllQuotaInMB":100,"bwLimitEnabled":0,"bwLimitRate":0,"customDNS":"","maxConnection":1000,"memo":"","default_position":true,"default_whitelistLimitAccess":true,"default_blacklistLimitAccess":true,"default_ipAuthentication":false,"default_userAuthentication":false,"default_portType":true,"default_ipType":true,"default_customDNS":true,"default_expiredDate":true,"default_limitAccess":true,"default_bwLimitEnabled":true,"default_counterDownloadLimit":true,"default_counterUploadLimit":true,"default_counterAllLimit":true,"default_maxConnection":false,"default_memo":true,"default_forceServerResolveDns":false,"default_userRotateSetting":false,"ids":[73]}
2025-08-26 14:10:52.245 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [http://aaaproxy-phl.aaaproxy.io:22021//selling/reset_data_counter]
2025-08-26 14:10:52.245 [[ForkJoinPool.commonPool-worker-1]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [http://aaaproxy-phl.aaaproxy.io:22021//selling/bulk_edit]
2025-08-26 14:10:52.245 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 14:10:52.245 [[ForkJoinPool.commonPool-worker-1]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 14:10:52.245 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Authorization: Basic YWFhcHJveHktcGEuaW86UGFzc2ZvckRldjIwMjUh]
2025-08-26 14:10:52.245 [[ForkJoinPool.commonPool-worker-1]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Authorization: Basic YWFhcHJveHktcGEuaW86UGFzc2ZvckRldjIwMjUh]
2025-08-26 14:10:52.245 [[ForkJoinPool.commonPool-worker-3]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [http://aaaproxy-phl.aaaproxy.io:22021//selling/bulk_edit]
2025-08-26 14:10:52.245 [[ForkJoinPool.commonPool-worker-3]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 14:10:52.245 [[ForkJoinPool.commonPool-worker-3]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Authorization: Basic YWFhcHJveHktcGEuaW86UGFzc2ZvckRldjIwMjUh]
2025-08-26 14:10:52.929 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [200]
2025-08-26 14:10:52.930 [[http-nio-8080-exec-7]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - Result response 1
2025-08-26 14:10:53.074 [[ForkJoinPool.commonPool-worker-3]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [200]
2025-08-26 14:10:53.074 [[ForkJoinPool.commonPool-worker-1]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [200]
2025-08-26 14:10:53.074 [[MyExecutor-2]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - Result response {"status":true,"data":[{"id":93,"position":1,"shared_port":37101,"port_type":"SocksV5","ip_type":"IPv46","auth_ip_list":"","auth_user_list":"uwfkpi8w:p34536","web_blacklist":"","web_whitelist":"","expired_at":"31/12/2049 12:00 GMT-5","counter_dl_limit":"unlimited","counter_dl_limit_by":"DAILY","counter_dl_quota":100,"counter_dl_bytes":202410456826,"counter_dl_updated_time":1755910592,"counter_dl_reset_time":0,"counter_ul_limit":"unlimited","counter_ul_limit_by":"DAILY","counter_ul_quota":100,"counter_ul_bytes":24416411779,"counter_ul_updated_time":1755910592,"counter_ul_reset_time":0,"bw_limit_enabled":false,"bw_limit_rate":1,"memo":"","created_at":"11/07/2025 22:02 GMT-4","custom_dns":"","counter_all_limit":"unlimited","counter_all_limit_by":"DAILY","counter_all_quota":100,"counter_all_bytes":226826868605,"counter_all_updated_time":1755910592,"counter_all_reset_time":0,"connection_limit":1000,"connection_limit_period":60,"api_token":"d35fe48f8a184244b40a92d2182f548f","force_server_resolve_dns":false,"interval_rotation":0,"last_rotation":null,"last_rotation_status":false,"allow_api_change_rotation":false,"allow_api_rotate":false,"min_interval_rotation":0,"min_second_rotate":0,"enabled":true,"tcp_os":"ipad1","gen_method":1,"group_positions":[],"auth_ip_enabled":false,"auth_user_enabled":true,"web_blacklist_enabled":false,"web_whitelist_enabled":false,"expired_at_unix":2524582800000,"remaining":8893.409104643843,"is_expired":false,"remaining_str":"24yr, 4mo, 13d, 9hr, 49min, 6s","counter_dl_limit_quota":100,"counter_dl_used_bytes":202410456826,"counter_dl_used_mb":193033.7,"counter_dl_updated":"23/08 00:56:32","counter_dl_reset":null,"counter_ul_limit_quota":100,"counter_ul_used_bytes":24416411779,"counter_ul_used_mb":23285.3,"counter_ul_updated":"23/08 00:56:32","counter_ul_reset":null,"counter_all_limit_quota":100,"counter_all_used_bytes":226826868605,"counter_all_used_mb":216319.0,"counter_all_updated":"23/08 00:56:32","counter_all_reset":null,"physical_port_status":"active"}]}

2025-08-26 14:10:53.074 [[MyExecutor-1]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - Result response {"status":true,"data":[{"id":73,"position":1,"shared_port":32101,"port_type":"HTTP","ip_type":"IPv46","auth_ip_list":"","auth_user_list":"uwfkpi8w:p34536","web_blacklist":"","web_whitelist":"","expired_at":"31/12/2049 12:00 GMT-5","counter_dl_limit":"unlimited","counter_dl_limit_by":"DAILY","counter_dl_quota":100,"counter_dl_bytes":50622871972,"counter_dl_updated_time":1754744244,"counter_dl_reset_time":0,"counter_ul_limit":"unlimited","counter_ul_limit_by":"DAILY","counter_ul_quota":100,"counter_ul_bytes":9028995420,"counter_ul_updated_time":1754744244,"counter_ul_reset_time":0,"bw_limit_enabled":false,"bw_limit_rate":1,"memo":"","created_at":"11/07/2025 22:02 GMT-4","custom_dns":"","counter_all_limit":"unlimited","counter_all_limit_by":"DAILY","counter_all_quota":100,"counter_all_bytes":59651867392,"counter_all_updated_time":1754744244,"counter_all_reset_time":0,"connection_limit":1000,"connection_limit_period":60,"api_token":"01a8761e63e94a6fb36774241fdb0d08","force_server_resolve_dns":false,"interval_rotation":0,"last_rotation":null,"last_rotation_status":false,"allow_api_change_rotation":false,"allow_api_rotate":false,"min_interval_rotation":0,"min_second_rotate":0,"enabled":true,"tcp_os":"ipad1","gen_method":1,"group_positions":[],"auth_ip_enabled":false,"auth_user_enabled":true,"web_blacklist_enabled":false,"web_whitelist_enabled":false,"expired_at_unix":2524582800000,"remaining":8893.409104789247,"is_expired":false,"remaining_str":"24yr, 4mo, 13d, 9hr, 49min, 6s","counter_dl_limit_quota":100,"counter_dl_used_bytes":50622871972,"counter_dl_used_mb":48277.7,"counter_dl_updated":"09/08 12:57:24","counter_dl_reset":null,"counter_ul_limit_quota":100,"counter_ul_used_bytes":9028995420,"counter_ul_used_mb":8610.7,"counter_ul_updated":"09/08 12:57:24","counter_ul_reset":null,"counter_all_limit_quota":100,"counter_all_used_bytes":59651867392,"counter_all_used_mb":56888.5,"counter_all_updated":"09/08 12:57:24","counter_all_reset":null,"physical_port_status":"active"}]}

2025-08-26 14:11:11.039 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:11:11.039 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:11:14.740 [[http-nio-8080-exec-7]] INFO  c.n.p.s.i.TransactionV2ServiceImpl - SEND EMAIL: null
2025-08-26 14:11:41.039 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:11:41.040 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:12:11.040 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:12:11.040 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:12:41.040 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:12:41.041 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:13:11.041 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:13:11.041 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:13:41.042 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:13:41.042 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:14:11.042 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:14:11.042 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:14:41.043 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:14:41.043 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:15:11.043 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:15:11.043 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:15:41.043 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:15:41.043 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:16:11.043 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:16:11.044 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:16:41.044 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:16:41.044 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:17:11.044 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:17:11.045 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:17:41.045 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:17:41.045 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:18:11.045 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=3, idle=7, waiting=0)
2025-08-26 14:18:11.045 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:18:41.046 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:18:41.046 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:19:11.047 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:19:11.047 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:19:41.047 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:19:41.047 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:20:11.473 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:20:11.473 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:20:41.473 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:20:41.474 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:21:11.474 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:21:11.474 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:21:41.475 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:21:41.475 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:22:11.475 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=2, idle=8, waiting=0)
2025-08-26 14:22:11.475 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:22:41.475 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:22:41.475 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:23:11.476 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:23:11.476 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:23:41.476 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:23:41.476 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:24:11.476 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:24:11.476 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:24:41.477 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:24:41.477 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:25:11.477 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:25:11.478 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:25:41.478 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:25:41.478 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:26:11.478 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:26:11.478 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:26:41.479 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:26:41.479 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:27:11.479 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:27:11.480 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:27:41.480 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:27:41.480 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:28:11.480 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:28:11.480 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:28:41.481 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:28:41.481 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:29:11.482 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:29:11.482 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:29:32.995 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@15c16f19: (connection has passed maxLifetime)
2025-08-26 14:29:36.201 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@7be1de54
2025-08-26 14:29:41.482 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:29:41.482 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:29:42.644 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@7c619b0f: (connection has passed maxLifetime)
2025-08-26 14:29:45.423 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@7fd5ee92
2025-08-26 14:29:47.252 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@50ca141a: (connection was evicted)
2025-08-26 14:29:50.044 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@71120f18
2025-08-26 14:29:50.044 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:29:53.006 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@42a6c28d: (connection has passed maxLifetime)
2025-08-26 14:29:55.233 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Add connection elided, waiting 0, queue 1
2025-08-26 14:29:55.233 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@2e65a290: (connection has passed maxLifetime)
2025-08-26 14:29:55.949 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@752d85e2
2025-08-26 14:29:58.861 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@5000fa6c
2025-08-26 14:29:58.861 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:30:05.916 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@67655119: (connection has passed maxLifetime)
2025-08-26 14:30:06.671 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Add connection elided, waiting 0, queue 1
2025-08-26 14:30:06.671 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@6a9da347: (connection has passed maxLifetime)
2025-08-26 14:30:09.147 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@6af16a61
2025-08-26 14:30:10.940 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Add connection elided, waiting 0, queue 1
2025-08-26 14:30:10.940 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@45a6c605: (connection has passed maxLifetime)
2025-08-26 14:30:11.482 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=8, active=0, idle=8, waiting=0)
2025-08-26 14:30:11.482 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:30:11.846 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@3bbeda31
2025-08-26 14:30:11.846 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=9, active=0, idle=9, waiting=0)
2025-08-26 14:30:15.142 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@125bbf65
2025-08-26 14:30:15.142 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:30:22.252 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@31c1e9b7: (connection was evicted)
2025-08-26 14:30:25.243 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@254e350b
2025-08-26 14:30:25.243 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:30:26.856 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@78e8097d: (connection has passed maxLifetime)
2025-08-26 14:30:29.864 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2b89cfd2
2025-08-26 14:30:41.483 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:30:41.483 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:31:07.510 [[SpringApplicationShutdownHook]] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-26 14:31:07.511 [[SpringApplicationShutdownHook]] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-26 14:31:07.511 [[SpringApplicationShutdownHook]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Before shutdown stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:31:07.511 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@7be1de54: (connection evicted)
2025-08-26 14:31:07.511 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@7fd5ee92: (connection evicted)
2025-08-26 14:31:07.511 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@71120f18: (connection evicted)
2025-08-26 14:31:07.511 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@752d85e2: (connection evicted)
2025-08-26 14:31:07.512 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@5000fa6c: (connection evicted)
2025-08-26 14:31:07.512 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@6af16a61: (connection evicted)
2025-08-26 14:31:07.512 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@3bbeda31: (connection evicted)
2025-08-26 14:31:07.512 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@125bbf65: (connection evicted)
2025-08-26 14:31:07.512 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@254e350b: (connection evicted)
2025-08-26 14:31:07.512 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@2b89cfd2: (connection evicted)
2025-08-26 14:31:07.512 [[SpringApplicationShutdownHook]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After shutdown stats (total=0, active=0, idle=0, waiting=0)
2025-08-26 14:31:07.512 [[SpringApplicationShutdownHook]] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-26 14:40:00.867 [[background-preinit]] DEBUG org.jboss.logging - Logging Provider: org.jboss.logging.Log4j2LoggerProvider
2025-08-26 14:40:00.868 [[background-preinit]] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final
2025-08-26 14:40:00.872 [[main]] INFO  c.n.pro.NeoProxyApiApplication - Starting NeoProxyApiApplication using Java 17.0.16 on vinhbui-HP-EliteBook-845-14-inch-G10-Notebook-PC with PID 50782 (/home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes started by vinhbui8 in /home/<USER>/Documents/aaaproxy/neo-proxy_app)
2025-08-26 14:40:00.872 [[main]] INFO  c.n.pro.NeoProxyApiApplication - The following profiles are active: dev
2025-08-26 14:40:01.304 [[main]] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-26 14:40:01.357 [[main]] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 50 ms. Found 14 JPA repository interfaces.
2025-08-26 14:40:01.634 [[main]] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-26 14:40:01.639 [[main]] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-26 14:40:01.639 [[main]] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-26 14:40:01.639 [[main]] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.56]
2025-08-26 14:40:01.677 [[main]] INFO  o.a.c.c.C.[.[localhost].[/neoproxy] - Initializing Spring embedded WebApplicationContext
2025-08-26 14:40:01.677 [[main]] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 783 ms
2025-08-26 14:40:01.700 [[main]] DEBUG i.m.c.u.i.l.InternalLoggerFactory - Using SLF4J as the default logging framework
2025-08-26 14:40:01.789 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - Driver class org.postgresql.Driver found in Thread context class loader TomcatEmbeddedWebappClassLoader
  context: neoproxy
  delegate: true
----------> Parent Classloader:
jdk.internal.loader.ClassLoaders$AppClassLoader@30946e09

2025-08-26 14:40:01.821 [[main]] DEBUG o.f.c.internal.util.FeatureDetector - AWS SDK available: false
2025-08-26 14:40:01.821 [[main]] DEBUG o.f.c.internal.util.FeatureDetector - Google Cloud Storage available: false
2025-08-26 14:40:01.822 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/callback' ...
2025-08-26 14:40:01.822 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/callback using ClassLoader TomcatEmbeddedWebappClassLoader
  context: neoproxy
  delegate: true
----------> Parent Classloader:
jdk.internal.loader.ClassLoaders$AppClassLoader@30946e09
 ...
2025-08-26 14:40:01.822 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Unable to resolve location classpath:db/callback.
2025-08-26 14:40:01.822 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/migration' ...
2025-08-26 14:40:01.822 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/migration using ClassLoader TomcatEmbeddedWebappClassLoader
  context: neoproxy
  delegate: true
----------> Parent Classloader:
jdk.internal.loader.ClassLoaders$AppClassLoader@30946e09
 ...
2025-08-26 14:40:01.822 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning URL: file:/home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes/db/migration
2025-08-26 14:40:01.822 [[main]] DEBUG o.f.c.internal.util.FeatureDetector - JBoss VFS v2 available: false
2025-08-26 14:40:01.823 [[main]] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning starting at classpath root in filesystem: /home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes/
2025-08-26 14:40:01.823 [[main]] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning for resources in path: /home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes/db/migration (db/migration)
2025-08-26 14:40:01.823 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V10__CREATE_TABLE_LOCATIONS.sql
2025-08-26 14:40:01.823 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V11__CREATE_TABLE_TRACKINGS.sql
2025-08-26 14:40:01.823 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V12__UPDATE_TABLE_TRANSACTIONS.sql
2025-08-26 14:40:01.823 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V13__UPDATE_TABLE_PROXYS.sql
2025-08-26 14:40:01.823 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V14__UPDATE_TABLE_PROXYS.sql
2025-08-26 14:40:01.823 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V15__UPDATE_TABLE_TRANSACTION.sql
2025-08-26 14:40:01.823 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V16__UPDATE_TABLE_CONFIG.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V17__CREATE_TABLE_PROMOTION.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V18__UPDATE_TABLE_TRANSACTION.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V19__UPDATE_TABLE_LICENSES.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V1__CREATE_TABLE_USERS.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V20__UPDATE_TABLE_USER.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V21__UPDATE_TABLE_TRANSACTION.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V22__UPDATE_TABLE_CONFIG.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V23__CREATE_TABLE_MAIL_TEMPLATE.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V24__UPDATE_TABLE_PROXYS.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V25__UPDATE_TABLE_MAIL.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V2__CREATE_TABLE_MODEMS.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V31__UPDATE_TABLE_LICENSE_FOR_SELLING.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V32__CREATE_TABLE_PROXYS.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V33__UPDATE_TABLE_PROXY_FOR_ISP.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V34__UPDATE_TABLE_PROXY_FOR_SPEED_TEST.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V35__UPDATE_TABLE_PACKAGES.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V36__UPDATE_TABLE_LICENSES.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V37__UPDATE_TABLE_LICENSES.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V38__UPDATE_TABLE_MAIL.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V39__UPDATE_TABLE_TRANSACTION.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V3__CREATE_TABLE_PROXY_WANS.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V40__UPDATE_TABLE_PROMOTION.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V41__UPDATE_TABLE_LICENSES_AUTO_RENEWAL.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V42__UPDATE_TABLE_PACKAGES_ADD_LOCATION_FEE.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V4__CREATE_TABLE_LICENSES.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V5__CREATE_TABLE_PACKAGES.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V6__CREATE_TABLE_TRANSACTIONS.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V7__CREATE_TABLE_MONITORS.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V8__CREATE_TABLE_REFRESH_TOKENS.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V9__CREATE_TABLE_CONFIGURATIONS.sql
2025-08-26 14:40:01.824 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classes at classpath:db/migration
2025-08-26 14:40:01.825 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-08-26 14:40:01.826 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension................................false
2025-08-26 14:40:01.826 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit................................true
2025-08-26 14:40:01.826 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - catalog................................none
2025-08-26 14:40:01.827 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql................................none
2025-08-26 14:40:01.827 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery................................none
2025-08-26 14:40:01.827 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout................................30000
2025-08-26 14:40:01.828 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSource................................none
2025-08-26 14:40:01.828 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName................................none
2025-08-26 14:40:01.828 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI................................none
2025-08-26 14:40:01.828 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties................................{password=<masked>}
2025-08-26 14:40:01.828 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................................"org.postgresql.Driver"
2025-08-26 14:40:01.828 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName................................none
2025-08-26 14:40:01.828 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties................................{}
2025-08-26 14:40:01.828 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry................................none
2025-08-26 14:40:01.828 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout................................***********-08-26 14:40:01.828 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout................................1
2025-08-26 14:40:01.829 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries................................false
2025-08-26 14:40:01.829 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl................................**************************************************************************************
2025-08-26 14:40:01.829 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime................................0
2025-08-26 14:40:01.829 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold................................0
2025-08-26 14:40:01.829 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime................................1800000
2025-08-26 14:40:01.829 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize................................10
2025-08-26 14:40:01.829 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry................................none
2025-08-26 14:40:01.829 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory................................none
2025-08-26 14:40:01.829 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle................................10
2025-08-26 14:40:01.829 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - password................................<masked>
2025-08-26 14:40:01.829 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - poolName................................"HikariPool-1"
2025-08-26 14:40:01.829 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - readOnly................................false
2025-08-26 14:40:01.829 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans................................false
2025-08-26 14:40:01.829 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor................................none
2025-08-26 14:40:01.829 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - schema................................none
2025-08-26 14:40:01.829 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory................................internal
2025-08-26 14:40:01.829 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation................................default
2025-08-26 14:40:01.829 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - username................................"proxydb"
2025-08-26 14:40:01.829 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout................................5000
2025-08-26 14:40:01.829 [[main]] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-26 14:40:05.615 [[main]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@1322b575
2025-08-26 14:40:05.616 [[main]] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-26 14:40:05.716 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=1, active=1, idle=0, waiting=0)
2025-08-26 14:40:06.231 [[main]] INFO  o.f.c.i.license.VersionPrinter - Flyway Community Edition 8.0.5 by Redgate
2025-08-26 14:40:06.231 [[main]] INFO  o.f.c.i.d.base.BaseDatabaseType - Database: ************************************************************************************** (PostgreSQL 13.15)
2025-08-26 14:40:06.231 [[main]] DEBUG o.f.c.i.d.base.BaseDatabaseType - Driver  : PostgreSQL JDBC Driver 42.3.1
2025-08-26 14:40:06.232 [[main]] DEBUG org.flywaydb.core.FlywayExecutor - DDL Transactions Supported: true
2025-08-26 14:40:06.233 [[main]] DEBUG o.f.c.i.s.SchemaHistoryFactory - Schemas: 
2025-08-26 14:40:06.233 [[main]] DEBUG o.f.c.i.s.SchemaHistoryFactory - Default schema: null
2025-08-26 14:40:08.648 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@50ca141a
2025-08-26 14:40:08.925 [[main]] DEBUG o.f.c.i.c.SqlScriptCallbackFactory - Scanning for SQL callbacks ...
2025-08-26 14:40:09.312 [[main]] DEBUG o.f.core.internal.command.DbValidate - Validating migrations ...
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V20__UPDATE_TABLE_USER.sql (filename: V20__UPDATE_TABLE_USER.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V31__UPDATE_TABLE_LICENSE_FOR_SELLING.sql (filename: V31__UPDATE_TABLE_LICENSE_FOR_SELLING.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V11__CREATE_TABLE_TRACKINGS.sql (filename: V11__CREATE_TABLE_TRACKINGS.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V2__CREATE_TABLE_MODEMS.sql (filename: V2__CREATE_TABLE_MODEMS.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V38__UPDATE_TABLE_MAIL.sql (filename: V38__UPDATE_TABLE_MAIL.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V25__UPDATE_TABLE_MAIL.sql (filename: V25__UPDATE_TABLE_MAIL.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V6__CREATE_TABLE_TRANSACTIONS.sql (filename: V6__CREATE_TABLE_TRANSACTIONS.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V18__UPDATE_TABLE_TRANSACTION.sql (filename: V18__UPDATE_TABLE_TRANSACTION.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V12__UPDATE_TABLE_TRANSACTIONS.sql (filename: V12__UPDATE_TABLE_TRANSACTIONS.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V39__UPDATE_TABLE_TRANSACTION.sql (filename: V39__UPDATE_TABLE_TRANSACTION.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V4__CREATE_TABLE_LICENSES.sql (filename: V4__CREATE_TABLE_LICENSES.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V22__UPDATE_TABLE_CONFIG.sql (filename: V22__UPDATE_TABLE_CONFIG.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V14__UPDATE_TABLE_PROXYS.sql (filename: V14__UPDATE_TABLE_PROXYS.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V8__CREATE_TABLE_REFRESH_TOKENS.sql (filename: V8__CREATE_TABLE_REFRESH_TOKENS.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V23__CREATE_TABLE_MAIL_TEMPLATE.sql (filename: V23__CREATE_TABLE_MAIL_TEMPLATE.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V35__UPDATE_TABLE_PACKAGES.sql (filename: V35__UPDATE_TABLE_PACKAGES.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V15__UPDATE_TABLE_TRANSACTION.sql (filename: V15__UPDATE_TABLE_TRANSACTION.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V17__CREATE_TABLE_PROMOTION.sql (filename: V17__CREATE_TABLE_PROMOTION.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V19__UPDATE_TABLE_LICENSES.sql (filename: V19__UPDATE_TABLE_LICENSES.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__CREATE_TABLE_USERS.sql (filename: V1__CREATE_TABLE_USERS.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V34__UPDATE_TABLE_PROXY_FOR_SPEED_TEST.sql (filename: V34__UPDATE_TABLE_PROXY_FOR_SPEED_TEST.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V16__UPDATE_TABLE_CONFIG.sql (filename: V16__UPDATE_TABLE_CONFIG.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V37__UPDATE_TABLE_LICENSES.sql (filename: V37__UPDATE_TABLE_LICENSES.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V10__CREATE_TABLE_LOCATIONS.sql (filename: V10__CREATE_TABLE_LOCATIONS.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V33__UPDATE_TABLE_PROXY_FOR_ISP.sql (filename: V33__UPDATE_TABLE_PROXY_FOR_ISP.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V5__CREATE_TABLE_PACKAGES.sql (filename: V5__CREATE_TABLE_PACKAGES.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V40__UPDATE_TABLE_PROMOTION.sql (filename: V40__UPDATE_TABLE_PROMOTION.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V32__CREATE_TABLE_PROXYS.sql (filename: V32__CREATE_TABLE_PROXYS.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V42__UPDATE_TABLE_PACKAGES_ADD_LOCATION_FEE.sql (filename: V42__UPDATE_TABLE_PACKAGES_ADD_LOCATION_FEE.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V9__CREATE_TABLE_CONFIGURATIONS.sql (filename: V9__CREATE_TABLE_CONFIGURATIONS.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V7__CREATE_TABLE_MONITORS.sql (filename: V7__CREATE_TABLE_MONITORS.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V41__UPDATE_TABLE_LICENSES_AUTO_RENEWAL.sql (filename: V41__UPDATE_TABLE_LICENSES_AUTO_RENEWAL.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V21__UPDATE_TABLE_TRANSACTION.sql (filename: V21__UPDATE_TABLE_TRANSACTION.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V24__UPDATE_TABLE_PROXYS.sql (filename: V24__UPDATE_TABLE_PROXYS.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V13__UPDATE_TABLE_PROXYS.sql (filename: V13__UPDATE_TABLE_PROXYS.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V36__UPDATE_TABLE_LICENSES.sql (filename: V36__UPDATE_TABLE_LICENSES.sql)
2025-08-26 14:40:09.324 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V3__CREATE_TABLE_PROXY_WANS.sql (filename: V3__CREATE_TABLE_PROXY_WANS.sql)
2025-08-26 14:40:10.497 [[main]] INFO  o.f.core.internal.command.DbValidate - Successfully validated 37 migrations (execution time 00:01.183s)
2025-08-26 14:40:11.729 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2e65a290
2025-08-26 14:40:12.596 [[main]] INFO  o.f.core.internal.command.DbMigrate - Current version of schema "public": 42
2025-08-26 14:40:12.854 [[main]] INFO  o.f.core.internal.command.DbMigrate - Schema "public" is up to date. No migration necessary.
2025-08-26 14:40:13.763 [[main]] DEBUG org.flywaydb.core.FlywayExecutor - Memory usage: 63 of 136M
2025-08-26 14:40:13.798 [[main]] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-26 14:40:13.819 [[main]] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.3.Final
2025-08-26 14:40:13.878 [[main]] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-26 14:40:13.918 [[main]] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQLDialect
2025-08-26 14:40:14.686 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@67655119
2025-08-26 14:40:14.839 [[main]] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-26 14:40:14.843 [[main]] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-26 14:40:15.352 [[main]] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-26 14:40:15.541 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/v1/rh-websocket/**'] with []
2025-08-26 14:40:15.541 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/index.html'] with []
2025-08-26 14:40:15.541 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/app.js'] with []
2025-08-26 14:40:15.541 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/favicon.ico'] with []
2025-08-26 14:40:15.559 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@17c3e33, org.springframework.security.web.context.SecurityContextPersistenceFilter@7421213d, org.springframework.security.web.header.HeaderWriterFilter@a8177f6, org.springframework.security.web.authentication.logout.LogoutFilter@2b01be54, com.neoproxy.pro.config.JwtRequestFilter@1315f1d5, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3a3f1703, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7d776105, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2eb27091, org.springframework.security.web.session.SessionManagementFilter@7955b4d4, org.springframework.security.web.access.ExceptionTranslationFilter@5339cdc6, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4a1cafb2]
2025-08-26 14:40:15.659 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.AuthenticationController:
	{POST [/v1/users/authentication/email]}: authenticateByEmail(EmailAuthenticationRequest)
2025-08-26 14:40:15.661 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.ISPController:
	{GET [/v1/isp/list]}: getLocations()
2025-08-26 14:40:15.662 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.LocationController:
	{POST [/v1/locations/list]}: getLocations()
	{GET [/v1/locations/list]}: getList()
	{GET [/v1/locations/full-list]}: getFullList()
2025-08-26 14:40:15.663 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.PackageController:
	{POST [/v1/packages/list]}: getPackages(PackageQueryRequest)
	{POST [/v1/packages]}: createNewPackage(PackageRequest)
	{PUT [/v1/packages/{uuid}]}: updatePackage(UUID,PackageRequest)
	{DELETE [/v1/packages/{uuid}]}: deletePackage(UUID)
2025-08-26 14:40:15.663 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.PingController:
	{GET [/v1/ping]}: ping()
2025-08-26 14:40:15.664 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.UserController:
	{GET [/v1/users/me]}: getLoggedUser()
	{POST [/v1/users/register]}: registerNewUser(NewUserRequest)
	{PUT [/v1/users/email]}: registerNewUserByEmail(UserEmailUpdateRequest)
	{PUT [/v1/users/reset-password]}: resetPassword(ResetPasswordRequest)
	{POST [/v1/users/change-password]}: changePassword(ResetPasswordRequest)
	{GET [/v1/users/verify/{code}]}: verifyAccount(String)
	{GET [/v1/users/change-reminder]}: changeReminder()
2025-08-26 14:40:15.665 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ConfigurationController:
	{PUT [/v1/admin/configurations/{uuid}]}: updateConfiguration(UUID,ConfigurationRequest)
	{POST [/v1/admin/configurations/list]}: getList(ConfigurationQueryRequest)
	{POST [/v1/admin/configurations/cate]}: getCate(ConfigurationQueryRequest)
	{PUT [/v1/admin/configurations/cate]}: updateListConf(Map)
	{POST [/v1/admin/configurations/send-notify-email]}: sendNotifyEmail(EmailNotifyRequest)
2025-08-26 14:40:15.666 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.CustomerController:
	{POST [/v1/admin/customers/{uuid}/active]}: active(UUID)
	{POST [/v1/admin/customers/list]}: getList(CustomerQueryRequest)
	{POST [/v1/admin/customers/{uuid}/topup/{amount}]}: topup(UUID,BigDecimal)
	{POST [/v1/admin/customers/{uuid}/refund/{amount}]}: refund(UUID,BigDecimal)
	{POST [/v1/admin/customers/{uuid}/suspended]}: suspended(UUID)
	{ [/v1/admin/customers/excel]}: excel(HttpServletResponse)
	{POST [/v1/admin/customers/{uuid}/reset-password/{new-password}]}: resetPassword(UUID,String)
	{GET [/v1/admin/customers/{uuid}]}: resetPassword(UUID)
2025-08-26 14:40:15.667 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.LicenseController:
	{ [/v1/admin/licenses/tracking]}: excel(HttpServletResponse,UUID)
	{POST [/v1/admin/licenses/list]}: getLicenses(LicenseQueryRequest,HttpServletRequest)
	{PUT [/v1/admin/licenses/{uuid}]}: updateLicense(UUID,LicenseRequest)
	{POST [/v1/admin/licenses/switch-modem]}: switchNewModem(SwitchModemRequest)
	{POST [/v1/admin/licenses/update-license]}: updateLicenseStatus(UpdateLicenseStatusRequest)
	{POST [/v1/admin/licenses/change-proxy]}: changeAvailablePort(LicenseChangeProxyRequest)
2025-08-26 14:40:15.668 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.MailTemplateController:
	{POST [/v1/admin/mail-templates/list]}: getList(MailTemplateQueryRequest)
	{POST [/v1/admin/mail-templates/send-notify-email]}: sendNotifyEmail(EmailNotifyRequest)
	{PUT [/v1/admin/mail-templates/{uuid}]}: updateMailTemplate(UUID,MailTemplateRequest)
	{POST [/v1/admin/mail-templates]}: insertMailTemplate(MailTemplateRequest)
2025-08-26 14:40:15.669 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ModemController:
	{DELETE [/v1/admin/modems/{uuid}]}: delete(UUID)
	{GET [/v1/admin/modems/{uuid}/resume]}: resume(UUID)
	{GET [/v1/admin/modems/{uuid}/sync]}: sync(UUID)
	{GET [/v1/admin/modems/{uuid}]}: detail(UUID)
	{POST [/v1/admin/modems]}: createNewModem(ModemRequest)
	{PUT [/v1/admin/modems/{uuid}]}: updateModem(UUID,ModemRequest)
	{POST [/v1/admin/modems/list]}: getModems(ModemQueryRequest)
	{GET [/v1/admin/modems/{uuid}/pause]}: pause(UUID)
	{POST [/v1/admin/modems/generate-port]}: generatePort(GeneratePortRequest)
2025-08-26 14:40:15.669 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.MonitorController:
	{POST [/v1/admin/monitors/list]}: getList(MonitorQueryRequest)
2025-08-26 14:40:15.670 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.OverviewController:
	{GET [/v1/admin/overview]}: overview()
2025-08-26 14:40:15.670 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.PromotionController:
	{POST [/v1/promotions]}: createNewPromotion(PromotionRequest)
	{PUT [/v1/promotions/{uuid}]}: updatePromotion(UUID,PromotionRequest)
	{POST [/v1/promotions/list]}: getPromotions(PromotionQueryRequest)
	{DELETE [/v1/promotions/{uuid}]}: deletePromotion(UUID)
2025-08-26 14:40:15.670 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ProxyController:
	{POST [/v1/admin/proxies/list]}: getProxyWans(ProxyQueryRequest)
	{DELETE [/v1/admin/proxies/delete]}: deleteProxies(ProxyRequest)
2025-08-26 14:40:15.670 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.TransactionController:
	{POST [/v1/admin/transactions/list]}: getList(TransactionQueryRequest)
2025-08-26 14:40:15.671 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ApiController:
	{GET [/v1/api/proxy/status]}: getStatus(String)
	{GET [/v1/api/proxy/change-ip]}: changeIp(String)
	{GET [/v1/api/proxy/change-rotation-time]}: changeRotationTime(String,Integer)
2025-08-26 14:40:15.672 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientLicenseController:
	{ [/v1/client/licenses/excel]}: excel(HttpServletResponse,String)
	{POST [/v1/client/licenses/change-rotation-time]}: changeRotationTime(ChangeRotationTimeRequest)
	{POST [/v1/client/licenses/list]}: getClientLicenses(LicenseQueryRequest)
	{POST [/v1/client/licenses/extend]}: renewIp(ExtendLicenseRequest)
	{POST [/v1/client/licenses/express-extend]}: expressExtend(ExpressExtendLicenseRequest)
	{POST [/v1/client/licenses/toggle-auto-renewal]}: toggleAutoRenewal(ToggleAutoRenewalRequest)
	{POST [/v1/client/licenses/purchase-vpn]}: purchaseVpn(PurchaseVpnRequest)
	{POST [/v1/client/licenses/cancel-vpn]}: cancelVpn(CancelVpnRequest)
	{POST [/v1/client/licenses/update-tcp-os]}: updateTcpOS(UpdateTcpOSRequest)
2025-08-26 14:40:15.672 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientPackageController:
	{POST [/v1/client/packages/list]}: getPackages(PackageQueryRequest)
2025-08-26 14:40:15.672 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientPromotionController:
	{GET [/v1/client/promotions/redeem-code]}: redeemCode(String,BigDecimal)
	{POST [/v1/client/promotions/quantity]}: getPromotionQuantity(PromotionQueryRequest)
	{GET [/v1/client/promotions/discount]}: getDiscountByPromotion(String,BigDecimal)
2025-08-26 14:40:15.673 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientProxyController:
	{POST [/v1/client/proxies/list]}: getProxyWans(ProxyQueryRequest)
	{POST [/v1/client/proxies/change-ip]}: changeProxyIp(ProxyRequest)
	{POST [/v1/client/proxies/reboot-device]}: rebootDevice(ProxyRequest)
	{POST [/v1/client/proxies/change-location]}: changeLocation(ProxyRequest)
	{POST [/v1/client/proxies/update-authenticate]}: updateAuthenticate(ProxyRequest)
	{POST [/v1/client/proxies/extend-license]}: extendLicense(ExtendLicenseRequest)
2025-08-26 14:40:15.673 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientTransactionController:
	{POST [/v1/client/transactions/list]}: getList(TransactionQueryRequest)
	{POST [/v1/client/transactions/place-order]}: createNewOrder(OrderProxyRequest)
2025-08-26 14:40:15.673 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientTransactionV2Controller:
	{POST [/v1/client/orders/place-order]}: checkOrder(OrderProxyV2Request)
2025-08-26 14:40:15.674 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.PaymentController:
	{POST [/v1/client/payments/topup]}: topup(TopupRequest)
	{GET [/v1/client/payments/currencies]}: getCurrencies()
	{GET [/v1/client/payments/full-currencies]}: getFullCurrencies()
	{GET [/v1/client/payments/minimum-amount]}: getMinimumAmount(String)
	{GET [/v1/client/payments/estimated-price]}: getEstimatedPrice(String,String,BigDecimal)
	{GET [/v1/client/payments/get-payment-url]}: getPaymentUrl(BigDecimal)
	{POST [/v1/client/payments/webhook]}: webhook(String,String)
2025-08-26 14:40:15.674 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.VpnController:
	{POST [/v1/client/vpn/change]}: change(VpnRequest)
	{GET [/v1/client/vpn/download]}: downloadFile(String)
2025-08-26 14:40:15.675 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error]}: error(HttpServletRequest)
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
2025-08-26 14:40:15.682 [[main]] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-08-26 14:40:15.691 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 'beanNameHandlerMapping' {}
2025-08-26 14:40:15.701 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 'resourceHandlerMapping' {/webjars/**=ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]], /**=ResourceHttpRequestHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/], ServletContext [/]]}
2025-08-26 14:40:15.842 [[main]] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 6 endpoint(s) beneath base path '/actuator'
2025-08-26 14:40:15.865 [[main]] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-26 14:40:15.870 [[main]] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/neoproxy'
2025-08-26 14:40:15.880 [[main]] INFO  c.n.pro.NeoProxyApiApplication - Started NeoProxyApiApplication in 15.187 seconds (JVM running for 15.506)
2025-08-26 14:40:17.521 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@31c1e9b7
2025-08-26 14:40:20.528 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@7c619b0f
2025-08-26 14:40:20.871 [[http-nio-8080-exec-1]] INFO  o.a.c.c.C.[.[localhost].[/neoproxy] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-26 14:40:20.871 [[http-nio-8080-exec-1]] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-26 14:40:20.880 [[http-nio-8080-exec-1]] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-26 14:40:23.235 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@7b519eb9
2025-08-26 14:40:26.250 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@1a54f4f8
2025-08-26 14:40:29.158 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@6d07055d
2025-08-26 14:40:32.435 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@42a6c28d
2025-08-26 14:40:32.436 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=10, active=2, idle=8, waiting=0)
2025-08-26 14:40:35.716 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=3, idle=7, waiting=0)
2025-08-26 14:40:35.716 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:40:44.948 [[http-nio-8080-exec-5]] INFO  c.n.p.s.singleton.SpeedTestService - ---LOCATION NEW YORK, NY | ISP T-Mobile | PROXY 1821 | SPEED TEST 7082609
2025-08-26 14:40:46.928 [[http-nio-8080-exec-8]] INFO  c.n.p.s.singleton.SpeedTestService - ---LOCATION NEW YORK, NY | ISP T-Mobile | PROXY 1821 | SPEED TEST 7082609
2025-08-26 14:41:02.770 [[http-nio-8080-exec-5]] INFO  c.n.p.s.singleton.SpeedTestService - ---LOCATION PHILADELPHIA, PA | ISP T-Mobile | PROXY 1752 | SPEED TEST 18894802
2025-08-26 14:41:03.748 [[http-nio-8080-exec-8]] INFO  c.n.p.s.singleton.SpeedTestService - ---LOCATION PHILADELPHIA, PA | ISP T-Mobile | PROXY 1752 | SPEED TEST 18894802
2025-08-26 14:41:05.717 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:41:05.718 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:41:35.718 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:41:35.718 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:42:05.719 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:42:05.719 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:42:35.719 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:42:35.720 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:42:59.707 [[http-nio-8080-exec-1]] INFO  c.n.p.c.client.ClientProxyController - ------ Change location with location 
2025-08-26 14:43:02.047 [[http-nio-8080-exec-1]] INFO  c.n.p.service.impl.ProxyServiceImpl - Total license list 1
2025-08-26 14:43:03.397 [[http-nio-8080-exec-1]] INFO  c.n.p.service.impl.ProxyServiceImpl - Location change fee 1 deducted from user d2a4ceaa-61dd-4cf2-8db0-25490f5d66ab balance for license ad38738e-9f98-4e31-87ab-efb0fbcaf6d3
2025-08-26 14:43:03.782 [[http-nio-8080-exec-1]] ERROR c.n.p.c.client.ClientProxyController - Create 
com.neoproxy.pro.service.exception.NeoProxyServiceException: The selected location does not have the available proxy. Please choose another location!
	at com.neoproxy.pro.service.impl.ProxyServiceImpl.changeLocationOfLicense(ProxyServiceImpl.java:654)
	at com.neoproxy.pro.service.impl.ProxyServiceImpl.changeProxyLocation(ProxyServiceImpl.java:580)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:208)
	at jdk.proxy2/jdk.proxy2.$Proxy176.changeProxyLocation(Unknown Source)
	at com.neoproxy.pro.controller.client.ClientProxyController.changeLocation(ClientProxyController.java:61)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.neoproxy.pro.config.JwtRequestFilter.doFilterInternal(JwtRequestFilter.java:33)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1732)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-26 14:43:05.720 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:43:05.720 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:43:10.604 [[http-nio-8080-exec-5]] INFO  c.n.p.c.client.ClientProxyController - ------ Change location with location PHILADELPHIA, PA
2025-08-26 14:43:12.655 [[http-nio-8080-exec-5]] INFO  c.n.p.service.impl.ProxyServiceImpl - Total license list 1
2025-08-26 14:43:13.999 [[http-nio-8080-exec-5]] INFO  c.n.p.service.impl.ProxyServiceImpl - Location change fee 1 deducted from user d2a4ceaa-61dd-4cf2-8db0-25490f5d66ab balance for license ad38738e-9f98-4e31-87ab-efb0fbcaf6d3
2025-08-26 14:43:22.559 [[http-nio-8080-exec-5]] INFO  c.n.p.service.impl.ProxyServiceImpl - _____Brother port of http port 32115 with sock port: 37115
2025-08-26 14:43:22.565 [[MyExecutor-1]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - ___ REQUEST: {"positionFrom":1,"positionTo":1,"numberOfPorts":1,"authMethod":0,"authEntry":"","ipAuthenticationEntry":"","userAuthenticationEntry":"ujauboal:p31111","portType":1,"ipType":1,"genPort":2,"genPortStart":30000,"expiredDate":2524582800000,"whitelistLimitAccessEntry":"","blacklistLimitAccessEntry":"","counterUploadLimit":0,"counterUploadLimitBy":1,"counterUploadQuotaInMB":100,"counterDownloadLimit":0,"counterDownloadLimitBy":1,"counterDownloadQuotaInMB":100,"counterAllLimit":0,"counterAllLimitBy":1,"counterAllQuotaInMB":100,"bwLimitEnabled":0,"bwLimitRate":0,"customDNS":"","maxConnection":1000,"memo":"","default_position":true,"default_whitelistLimitAccess":true,"default_blacklistLimitAccess":true,"default_ipAuthentication":false,"default_userAuthentication":false,"default_portType":true,"default_ipType":true,"default_customDNS":true,"default_expiredDate":true,"default_limitAccess":true,"default_bwLimitEnabled":true,"default_counterDownloadLimit":true,"default_counterUploadLimit":true,"default_counterAllLimit":true,"default_maxConnection":false,"default_memo":true,"default_forceServerResolveDns":false,"default_userRotateSetting":false,"ids":[73,93]}
2025-08-26 14:43:22.571 [[ForkJoinPool.commonPool-worker-1]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [http://aaaproxy-phl.aaaproxy.io:22021//selling/bulk_edit]
2025-08-26 14:43:22.571 [[ForkJoinPool.commonPool-worker-1]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 14:43:22.571 [[ForkJoinPool.commonPool-worker-1]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Authorization: Basic YWFhcHJveHktcGEuaW86UGFzc2ZvckRldjIwMjUh]
2025-08-26 14:43:23.368 [[ForkJoinPool.commonPool-worker-1]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [200]
2025-08-26 14:43:23.369 [[MyExecutor-1]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - Result response {"status":true,"data":[{"id":73,"position":1,"shared_port":32101,"port_type":"HTTP","ip_type":"IPv46","auth_ip_list":"","auth_user_list":"ujauboal:p31111","web_blacklist":"","web_whitelist":"","expired_at":"31/12/2049 12:00 GMT-5","counter_dl_limit":"unlimited","counter_dl_limit_by":"DAILY","counter_dl_quota":100,"counter_dl_bytes":0,"counter_dl_updated_time":0,"counter_dl_reset_time":0,"counter_ul_limit":"unlimited","counter_ul_limit_by":"DAILY","counter_ul_quota":100,"counter_ul_bytes":0,"counter_ul_updated_time":0,"counter_ul_reset_time":0,"bw_limit_enabled":false,"bw_limit_rate":1,"memo":"","created_at":"11/07/2025 22:02 GMT-4","custom_dns":"","counter_all_limit":"unlimited","counter_all_limit_by":"DAILY","counter_all_quota":100,"counter_all_bytes":0,"counter_all_updated_time":0,"counter_all_reset_time":0,"connection_limit":1000,"connection_limit_period":60,"api_token":"01a8761e63e94a6fb36774241fdb0d08","force_server_resolve_dns":false,"interval_rotation":0,"last_rotation":null,"last_rotation_status":false,"allow_api_change_rotation":false,"allow_api_rotate":false,"min_interval_rotation":0,"min_second_rotate":0,"enabled":true,"tcp_os":"ipad1","gen_method":1,"group_positions":[],"auth_ip_enabled":false,"auth_user_enabled":true,"web_blacklist_enabled":false,"web_whitelist_enabled":false,"expired_at_unix":2524582800000,"remaining":8893.386536715498,"is_expired":false,"remaining_str":"24yr, 4mo, 13d, 9hr, 16min, 36s","counter_dl_limit_quota":100,"counter_dl_used_bytes":0,"counter_dl_used_mb":0.0,"counter_dl_updated":null,"counter_dl_reset":null,"counter_ul_limit_quota":100,"counter_ul_used_bytes":0,"counter_ul_used_mb":0.0,"counter_ul_updated":null,"counter_ul_reset":null,"counter_all_limit_quota":100,"counter_all_used_bytes":0,"counter_all_used_mb":0.0,"counter_all_updated":null,"counter_all_reset":null,"physical_port_status":"active"},{"id":93,"position":1,"shared_port":37101,"port_type":"SocksV5","ip_type":"IPv46","auth_ip_list":"","auth_user_list":"ujauboal:p31111","web_blacklist":"","web_whitelist":"","expired_at":"31/12/2049 12:00 GMT-5","counter_dl_limit":"unlimited","counter_dl_limit_by":"DAILY","counter_dl_quota":100,"counter_dl_bytes":0,"counter_dl_updated_time":0,"counter_dl_reset_time":0,"counter_ul_limit":"unlimited","counter_ul_limit_by":"DAILY","counter_ul_quota":100,"counter_ul_bytes":0,"counter_ul_updated_time":0,"counter_ul_reset_time":0,"bw_limit_enabled":false,"bw_limit_rate":1,"memo":"","created_at":"11/07/2025 22:02 GMT-4","custom_dns":"","counter_all_limit":"unlimited","counter_all_limit_by":"DAILY","counter_all_quota":100,"counter_all_bytes":0,"counter_all_updated_time":0,"counter_all_reset_time":0,"connection_limit":1000,"connection_limit_period":60,"api_token":"d35fe48f8a184244b40a92d2182f548f","force_server_resolve_dns":false,"interval_rotation":0,"last_rotation":null,"last_rotation_status":false,"allow_api_change_rotation":false,"allow_api_rotate":false,"min_interval_rotation":0,"min_second_rotate":0,"enabled":true,"tcp_os":"ipad1","gen_method":1,"group_positions":[],"auth_ip_enabled":false,"auth_user_enabled":true,"web_blacklist_enabled":false,"web_whitelist_enabled":false,"expired_at_unix":2524582800000,"remaining":8893.386536714144,"is_expired":false,"remaining_str":"24yr, 4mo, 13d, 9hr, 16min, 36s","counter_dl_limit_quota":100,"counter_dl_used_bytes":0,"counter_dl_used_mb":0.0,"counter_dl_updated":null,"counter_dl_reset":null,"counter_ul_limit_quota":100,"counter_ul_used_bytes":0,"counter_ul_used_mb":0.0,"counter_ul_updated":null,"counter_ul_reset":null,"counter_all_limit_quota":100,"counter_all_used_bytes":0,"counter_all_used_mb":0.0,"counter_all_updated":null,"counter_all_reset":null,"physical_port_status":"active"}]}

2025-08-26 14:43:24.501 [[http-nio-8080-exec-5]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - ___ REQUEST: {"positionFrom":1,"positionTo":1,"numberOfPorts":1,"authMethod":0,"authEntry":"","ipAuthenticationEntry":"","userAuthenticationEntry":"uwfkpi8w:p34536","portType":1,"ipType":1,"genPort":2,"genPortStart":30000,"expiredDate":2524582800000,"whitelistLimitAccessEntry":"","blacklistLimitAccessEntry":"","counterUploadLimit":0,"counterUploadLimitBy":1,"counterUploadQuotaInMB":100,"counterDownloadLimit":0,"counterDownloadLimitBy":1,"counterDownloadQuotaInMB":100,"counterAllLimit":0,"counterAllLimitBy":1,"counterAllQuotaInMB":100,"bwLimitEnabled":0,"bwLimitRate":0,"customDNS":"","maxConnection":1000,"memo":"","default_position":true,"default_whitelistLimitAccess":true,"default_blacklistLimitAccess":true,"default_ipAuthentication":false,"default_userAuthentication":false,"default_portType":true,"default_ipType":true,"default_customDNS":true,"default_expiredDate":true,"default_limitAccess":true,"default_bwLimitEnabled":true,"default_counterDownloadLimit":true,"default_counterUploadLimit":true,"default_counterAllLimit":true,"default_maxConnection":false,"default_memo":true,"default_forceServerResolveDns":false,"default_userRotateSetting":false,"ids":[87]}
2025-08-26 14:43:24.501 [[ForkJoinPool.commonPool-worker-1]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [http://aaaproxy-phl.aaaproxy.io:22021//selling/bulk_edit]
2025-08-26 14:43:24.501 [[ForkJoinPool.commonPool-worker-1]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 14:43:24.501 [[ForkJoinPool.commonPool-worker-1]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Authorization: Basic YWFhcHJveHktcGEuaW86UGFzc2ZvckRldjIwMjUh]
2025-08-26 14:43:25.114 [[ForkJoinPool.commonPool-worker-1]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [200]
2025-08-26 14:43:25.114 [[http-nio-8080-exec-5]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - Result response {"status":true,"data":[{"id":87,"position":15,"shared_port":32115,"port_type":"HTTP","ip_type":"IPv4","auth_ip_list":"","auth_user_list":"uwfkpi8w:p34536","web_blacklist":"","web_whitelist":"","expired_at":"31/12/2049 12:00 GMT-5","counter_dl_limit":"unlimited","counter_dl_limit_by":"DAILY","counter_dl_quota":100,"counter_dl_bytes":0,"counter_dl_updated_time":0,"counter_dl_reset_time":0,"counter_ul_limit":"unlimited","counter_ul_limit_by":"DAILY","counter_ul_quota":100,"counter_ul_bytes":0,"counter_ul_updated_time":0,"counter_ul_reset_time":0,"bw_limit_enabled":false,"bw_limit_rate":1,"memo":"","created_at":"11/07/2025 22:02 GMT-4","custom_dns":"","counter_all_limit":"unlimited","counter_all_limit_by":"DAILY","counter_all_quota":100,"counter_all_bytes":0,"counter_all_updated_time":0,"counter_all_reset_time":0,"connection_limit":1000,"connection_limit_period":60,"api_token":"cf49dcd8e4de4bf9a16a5c68eae99878","force_server_resolve_dns":false,"interval_rotation":0,"last_rotation":null,"last_rotation_status":false,"allow_api_change_rotation":false,"allow_api_rotate":false,"min_interval_rotation":0,"min_second_rotate":0,"enabled":true,"tcp_os":"linux","gen_method":1,"group_positions":[],"auth_ip_enabled":false,"auth_user_enabled":true,"web_blacklist_enabled":false,"web_whitelist_enabled":false,"expired_at_unix":2524582800000,"remaining":8893.386516389954,"is_expired":false,"remaining_str":"24yr, 4mo, 13d, 9hr, 16min, 35s","counter_dl_limit_quota":100,"counter_dl_used_bytes":0,"counter_dl_used_mb":0.0,"counter_dl_updated":null,"counter_dl_reset":null,"counter_ul_limit_quota":100,"counter_ul_used_bytes":0,"counter_ul_used_mb":0.0,"counter_ul_updated":null,"counter_ul_reset":null,"counter_all_limit_quota":100,"counter_all_used_bytes":0,"counter_all_used_mb":0.0,"counter_all_updated":null,"counter_all_reset":null,"physical_port_status":"active"}]}

2025-08-26 14:43:26.222 [[http-nio-8080-exec-5]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - ___ REQUEST: {"positionFrom":1,"positionTo":1,"numberOfPorts":1,"authMethod":0,"authEntry":"","ipAuthenticationEntry":"","userAuthenticationEntry":"uwfkpi8w:p34536","portType":1,"ipType":1,"genPort":2,"genPortStart":30000,"expiredDate":2524582800000,"whitelistLimitAccessEntry":"","blacklistLimitAccessEntry":"","counterUploadLimit":0,"counterUploadLimitBy":1,"counterUploadQuotaInMB":100,"counterDownloadLimit":0,"counterDownloadLimitBy":1,"counterDownloadQuotaInMB":100,"counterAllLimit":0,"counterAllLimitBy":1,"counterAllQuotaInMB":100,"bwLimitEnabled":0,"bwLimitRate":0,"customDNS":"","maxConnection":1000,"memo":"","default_position":true,"default_whitelistLimitAccess":true,"default_blacklistLimitAccess":true,"default_ipAuthentication":false,"default_userAuthentication":false,"default_portType":true,"default_ipType":true,"default_customDNS":true,"default_expiredDate":true,"default_limitAccess":true,"default_bwLimitEnabled":true,"default_counterDownloadLimit":true,"default_counterUploadLimit":true,"default_counterAllLimit":true,"default_maxConnection":false,"default_memo":true,"default_forceServerResolveDns":false,"default_userRotateSetting":false,"ids":[107]}
2025-08-26 14:43:26.222 [[ForkJoinPool.commonPool-worker-1]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [http://aaaproxy-phl.aaaproxy.io:22021//selling/bulk_edit]
2025-08-26 14:43:26.230 [[ForkJoinPool.commonPool-worker-1]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 14:43:26.230 [[ForkJoinPool.commonPool-worker-1]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Authorization: Basic YWFhcHJveHktcGEuaW86UGFzc2ZvckRldjIwMjUh]
2025-08-26 14:43:26.883 [[ForkJoinPool.commonPool-worker-1]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [200]
2025-08-26 14:43:26.883 [[http-nio-8080-exec-5]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - Result response {"status":true,"data":[{"id":107,"position":15,"shared_port":37115,"port_type":"SocksV5","ip_type":"IPv4","auth_ip_list":"","auth_user_list":"uwfkpi8w:p34536","web_blacklist":"","web_whitelist":"","expired_at":"31/12/2049 12:00 GMT-5","counter_dl_limit":"unlimited","counter_dl_limit_by":"DAILY","counter_dl_quota":100,"counter_dl_bytes":0,"counter_dl_updated_time":0,"counter_dl_reset_time":0,"counter_ul_limit":"unlimited","counter_ul_limit_by":"DAILY","counter_ul_quota":100,"counter_ul_bytes":0,"counter_ul_updated_time":0,"counter_ul_reset_time":0,"bw_limit_enabled":false,"bw_limit_rate":1,"memo":"","created_at":"11/07/2025 22:02 GMT-4","custom_dns":"","counter_all_limit":"unlimited","counter_all_limit_by":"DAILY","counter_all_quota":100,"counter_all_bytes":0,"counter_all_updated_time":0,"counter_all_reset_time":0,"connection_limit":1000,"connection_limit_period":60,"api_token":"11fed08f8b504f8fa8f80bf24ed48243","force_server_resolve_dns":false,"interval_rotation":0,"last_rotation":null,"last_rotation_status":false,"allow_api_change_rotation":false,"allow_api_rotate":false,"min_interval_rotation":0,"min_second_rotate":0,"enabled":true,"tcp_os":"linux","gen_method":1,"group_positions":[],"auth_ip_enabled":false,"auth_user_enabled":true,"web_blacklist_enabled":false,"web_whitelist_enabled":false,"expired_at_unix":2524582800000,"remaining":8893.386496088808,"is_expired":false,"remaining_str":"24yr, 4mo, 13d, 9hr, 16min, 33s","counter_dl_limit_quota":100,"counter_dl_used_bytes":0,"counter_dl_used_mb":0.0,"counter_dl_updated":null,"counter_dl_reset":null,"counter_ul_limit_quota":100,"counter_ul_used_bytes":0,"counter_ul_used_mb":0.0,"counter_ul_updated":null,"counter_ul_reset":null,"counter_all_limit_quota":100,"counter_all_used_bytes":0,"counter_all_used_mb":0.0,"counter_all_updated":null,"counter_all_reset":null,"physical_port_status":"active"}]}

2025-08-26 14:43:27.393 [[ForkJoinPool.commonPool-worker-1]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [http://aaaproxy-phl.aaaproxy.io:22021//selling/reset_data_counter]
2025-08-26 14:43:27.393 [[ForkJoinPool.commonPool-worker-1]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 14:43:27.393 [[ForkJoinPool.commonPool-worker-1]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Authorization: Basic YWFhcHJveHktcGEuaW86UGFzc2ZvckRldjIwMjUh]
2025-08-26 14:43:27.982 [[ForkJoinPool.commonPool-worker-1]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [200]
2025-08-26 14:43:27.982 [[http-nio-8080-exec-5]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - Result response 1
2025-08-26 14:43:35.720 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:43:35.720 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:44:05.721 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:44:05.721 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:44:35.721 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:44:35.721 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:45:05.722 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:45:05.722 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:45:35.722 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:45:35.722 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:46:05.723 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:46:05.723 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:46:35.732 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:46:35.732 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:47:05.733 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:47:05.733 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:47:35.733 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:47:35.733 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:48:05.734 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:48:05.734 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:48:35.734 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:48:35.735 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:49:05.735 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:49:05.735 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:49:35.735 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:49:35.736 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:50:05.736 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:50:05.736 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:50:35.736 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:50:35.736 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:50:57.677 [[http-nio-8080-exec-2]] INFO  c.n.p.c.client.ClientProxyController - ------ Change location with location NEW YORK, NY
2025-08-26 14:50:59.729 [[http-nio-8080-exec-2]] INFO  c.n.p.service.impl.ProxyServiceImpl - Total license list 1
2025-08-26 14:51:00.913 [[http-nio-8080-exec-2]] INFO  c.n.p.service.impl.ProxyServiceImpl - Location change fee 1 deducted from user d2a4ceaa-61dd-4cf2-8db0-25490f5d66ab balance for license ad38738e-9f98-4e31-87ab-efb0fbcaf6d3
2025-08-26 14:51:05.736 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:51:05.737 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:51:13.079 [[http-nio-8080-exec-2]] INFO  c.n.p.service.impl.ProxyServiceImpl - _____Brother port of http port 42110 with sock port: 47110
2025-08-26 14:51:13.079 [[MyExecutor-2]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - ___ REQUEST: {"positionFrom":1,"positionTo":1,"numberOfPorts":1,"authMethod":0,"authEntry":"","ipAuthenticationEntry":"","userAuthenticationEntry":"umhss3hx:p28226","portType":1,"ipType":1,"genPort":2,"genPortStart":30000,"expiredDate":2524582800000,"whitelistLimitAccessEntry":"","blacklistLimitAccessEntry":"","counterUploadLimit":0,"counterUploadLimitBy":1,"counterUploadQuotaInMB":100,"counterDownloadLimit":0,"counterDownloadLimitBy":1,"counterDownloadQuotaInMB":100,"counterAllLimit":0,"counterAllLimitBy":1,"counterAllQuotaInMB":100,"bwLimitEnabled":0,"bwLimitRate":0,"customDNS":"","maxConnection":1000,"memo":"","default_position":true,"default_whitelistLimitAccess":true,"default_blacklistLimitAccess":true,"default_ipAuthentication":false,"default_userAuthentication":false,"default_portType":true,"default_ipType":true,"default_customDNS":true,"default_expiredDate":true,"default_limitAccess":true,"default_bwLimitEnabled":true,"default_counterDownloadLimit":true,"default_counterUploadLimit":true,"default_counterAllLimit":true,"default_maxConnection":false,"default_memo":true,"default_forceServerResolveDns":false,"default_userRotateSetting":false,"ids":[87,107]}
2025-08-26 14:51:13.080 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [http://aaaproxy-phl.aaaproxy.io:22021//selling/bulk_edit]
2025-08-26 14:51:13.080 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 14:51:13.080 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Authorization: Basic YWFhcHJveHktcGEuaW86UGFzc2ZvckRldjIwMjUh]
2025-08-26 14:51:13.956 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [200]
2025-08-26 14:51:13.956 [[MyExecutor-2]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - Result response {"status":true,"data":[{"id":87,"position":15,"shared_port":32115,"port_type":"HTTP","ip_type":"IPv4","auth_ip_list":"","auth_user_list":"umhss3hx:p28226","web_blacklist":"","web_whitelist":"","expired_at":"31/12/2049 12:00 GMT-5","counter_dl_limit":"unlimited","counter_dl_limit_by":"DAILY","counter_dl_quota":100,"counter_dl_bytes":0,"counter_dl_updated_time":0,"counter_dl_reset_time":0,"counter_ul_limit":"unlimited","counter_ul_limit_by":"DAILY","counter_ul_quota":100,"counter_ul_bytes":0,"counter_ul_updated_time":0,"counter_ul_reset_time":0,"bw_limit_enabled":false,"bw_limit_rate":1,"memo":"","created_at":"11/07/2025 22:02 GMT-4","custom_dns":"","counter_all_limit":"unlimited","counter_all_limit_by":"DAILY","counter_all_quota":100,"counter_all_bytes":0,"counter_all_updated_time":0,"counter_all_reset_time":0,"connection_limit":1000,"connection_limit_period":60,"api_token":"cf49dcd8e4de4bf9a16a5c68eae99878","force_server_resolve_dns":false,"interval_rotation":0,"last_rotation":null,"last_rotation_status":false,"allow_api_change_rotation":false,"allow_api_rotate":false,"min_interval_rotation":0,"min_second_rotate":0,"enabled":true,"tcp_os":"linux","gen_method":1,"group_positions":[],"auth_ip_enabled":false,"auth_user_enabled":true,"web_blacklist_enabled":false,"web_whitelist_enabled":false,"expired_at_unix":2524582800000,"remaining":8893.381090691944,"is_expired":false,"remaining_str":"24yr, 4mo, 13d, 9hr, 8min, 46s","counter_dl_limit_quota":100,"counter_dl_used_bytes":0,"counter_dl_used_mb":0.0,"counter_dl_updated":null,"counter_dl_reset":null,"counter_ul_limit_quota":100,"counter_ul_used_bytes":0,"counter_ul_used_mb":0.0,"counter_ul_updated":null,"counter_ul_reset":null,"counter_all_limit_quota":100,"counter_all_used_bytes":0,"counter_all_used_mb":0.0,"counter_all_updated":null,"counter_all_reset":null,"physical_port_status":"active"},{"id":107,"position":15,"shared_port":37115,"port_type":"SocksV5","ip_type":"IPv4","auth_ip_list":"","auth_user_list":"umhss3hx:p28226","web_blacklist":"","web_whitelist":"","expired_at":"31/12/2049 12:00 GMT-5","counter_dl_limit":"unlimited","counter_dl_limit_by":"DAILY","counter_dl_quota":100,"counter_dl_bytes":0,"counter_dl_updated_time":0,"counter_dl_reset_time":0,"counter_ul_limit":"unlimited","counter_ul_limit_by":"DAILY","counter_ul_quota":100,"counter_ul_bytes":0,"counter_ul_updated_time":0,"counter_ul_reset_time":0,"bw_limit_enabled":false,"bw_limit_rate":1,"memo":"","created_at":"11/07/2025 22:02 GMT-4","custom_dns":"","counter_all_limit":"unlimited","counter_all_limit_by":"DAILY","counter_all_quota":100,"counter_all_bytes":0,"counter_all_updated_time":0,"counter_all_reset_time":0,"connection_limit":1000,"connection_limit_period":60,"api_token":"11fed08f8b504f8fa8f80bf24ed48243","force_server_resolve_dns":false,"interval_rotation":0,"last_rotation":null,"last_rotation_status":false,"allow_api_change_rotation":false,"allow_api_rotate":false,"min_interval_rotation":0,"min_second_rotate":0,"enabled":true,"tcp_os":"linux","gen_method":1,"group_positions":[],"auth_ip_enabled":false,"auth_user_enabled":true,"web_blacklist_enabled":false,"web_whitelist_enabled":false,"expired_at_unix":2524582800000,"remaining":8893.381090688774,"is_expired":false,"remaining_str":"24yr, 4mo, 13d, 9hr, 8min, 46s","counter_dl_limit_quota":100,"counter_dl_used_bytes":0,"counter_dl_used_mb":0.0,"counter_dl_updated":null,"counter_dl_reset":null,"counter_ul_limit_quota":100,"counter_ul_used_bytes":0,"counter_ul_used_mb":0.0,"counter_ul_updated":null,"counter_ul_reset":null,"counter_all_limit_quota":100,"counter_all_used_bytes":0,"counter_all_used_mb":0.0,"counter_all_updated":null,"counter_all_reset":null,"physical_port_status":"active"}]}

2025-08-26 14:51:15.056 [[http-nio-8080-exec-2]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - ___ REQUEST: {"positionFrom":1,"positionTo":1,"numberOfPorts":1,"authMethod":0,"authEntry":"","ipAuthenticationEntry":"","userAuthenticationEntry":"uwfkpi8w:p34536","portType":1,"ipType":1,"genPort":2,"genPortStart":30000,"expiredDate":2524582800000,"whitelistLimitAccessEntry":"","blacklistLimitAccessEntry":"","counterUploadLimit":0,"counterUploadLimitBy":1,"counterUploadQuotaInMB":100,"counterDownloadLimit":0,"counterDownloadLimitBy":1,"counterDownloadQuotaInMB":100,"counterAllLimit":0,"counterAllLimitBy":1,"counterAllQuotaInMB":100,"bwLimitEnabled":0,"bwLimitRate":0,"customDNS":"","maxConnection":1000,"memo":"","default_position":true,"default_whitelistLimitAccess":true,"default_blacklistLimitAccess":true,"default_ipAuthentication":false,"default_userAuthentication":false,"default_portType":true,"default_ipType":true,"default_customDNS":true,"default_expiredDate":true,"default_limitAccess":true,"default_bwLimitEnabled":true,"default_counterDownloadLimit":true,"default_counterUploadLimit":true,"default_counterAllLimit":true,"default_maxConnection":false,"default_memo":true,"default_forceServerResolveDns":false,"default_userRotateSetting":false,"ids":[10]}
2025-08-26 14:51:15.056 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [http://aaaproxy-jfk.aaaproxy.io:22031//selling/bulk_edit]
2025-08-26 14:51:15.056 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 14:51:15.056 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Authorization: Basic YWFhcHJveHktamZrLmlvOlBhc3Nmb3JEZXYyMDI1IQ==]
2025-08-26 14:51:15.766 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [200]
2025-08-26 14:51:15.766 [[http-nio-8080-exec-2]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - Result response {"status":true,"data":[{"id":10,"position":10,"shared_port":42110,"port_type":"HTTP","ip_type":"IPv4","auth_ip_list":"","auth_user_list":"uwfkpi8w:p34536","web_blacklist":"","web_whitelist":"","expired_at":"31/12/2049 12:00 GMT-5","counter_dl_limit":"unlimited","counter_dl_limit_by":"DAILY","counter_dl_quota":100,"counter_dl_bytes":0,"counter_dl_updated_time":0,"counter_dl_reset_time":0,"counter_ul_limit":"unlimited","counter_ul_limit_by":"DAILY","counter_ul_quota":100,"counter_ul_bytes":0,"counter_ul_updated_time":0,"counter_ul_reset_time":0,"bw_limit_enabled":false,"bw_limit_rate":1,"memo":"","created_at":"12/07/2025 00:58 GMT-4","custom_dns":"","counter_all_limit":"unlimited","counter_all_limit_by":"DAILY","counter_all_quota":100,"counter_all_bytes":0,"counter_all_updated_time":0,"counter_all_reset_time":0,"connection_limit":1000,"connection_limit_period":60,"api_token":"ca61abb1735f42ae8c9ff6eb057b03b6","force_server_resolve_dns":false,"interval_rotation":0,"last_rotation":null,"last_rotation_status":false,"allow_api_change_rotation":false,"allow_api_rotate":false,"min_interval_rotation":0,"min_second_rotate":0,"enabled":true,"tcp_os":"linux","gen_method":1,"group_positions":[],"auth_ip_enabled":false,"auth_user_enabled":true,"web_blacklist_enabled":false,"web_whitelist_enabled":false,"expired_at_unix":2524582800000,"remaining":8893.381068573044,"is_expired":false,"remaining_str":"24yr, 4mo, 13d, 9hr, 8min, 44s","counter_dl_limit_quota":100,"counter_dl_used_bytes":0,"counter_dl_used_mb":0.0,"counter_dl_updated":null,"counter_dl_reset":null,"counter_ul_limit_quota":100,"counter_ul_used_bytes":0,"counter_ul_used_mb":0.0,"counter_ul_updated":null,"counter_ul_reset":null,"counter_all_limit_quota":100,"counter_all_used_bytes":0,"counter_all_used_mb":0.0,"counter_all_updated":null,"counter_all_reset":null,"physical_port_status":"active"}]}

2025-08-26 14:51:16.890 [[http-nio-8080-exec-2]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - ___ REQUEST: {"positionFrom":1,"positionTo":1,"numberOfPorts":1,"authMethod":0,"authEntry":"","ipAuthenticationEntry":"","userAuthenticationEntry":"uwfkpi8w:p34536","portType":1,"ipType":1,"genPort":2,"genPortStart":30000,"expiredDate":2524582800000,"whitelistLimitAccessEntry":"","blacklistLimitAccessEntry":"","counterUploadLimit":0,"counterUploadLimitBy":1,"counterUploadQuotaInMB":100,"counterDownloadLimit":0,"counterDownloadLimitBy":1,"counterDownloadQuotaInMB":100,"counterAllLimit":0,"counterAllLimitBy":1,"counterAllQuotaInMB":100,"bwLimitEnabled":0,"bwLimitRate":0,"customDNS":"","maxConnection":1000,"memo":"","default_position":true,"default_whitelistLimitAccess":true,"default_blacklistLimitAccess":true,"default_ipAuthentication":false,"default_userAuthentication":false,"default_portType":true,"default_ipType":true,"default_customDNS":true,"default_expiredDate":true,"default_limitAccess":true,"default_bwLimitEnabled":true,"default_counterDownloadLimit":true,"default_counterUploadLimit":true,"default_counterAllLimit":true,"default_maxConnection":false,"default_memo":true,"default_forceServerResolveDns":false,"default_userRotateSetting":false,"ids":[30]}
2025-08-26 14:51:16.890 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [http://aaaproxy-jfk.aaaproxy.io:22031//selling/bulk_edit]
2025-08-26 14:51:16.890 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 14:51:16.890 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Authorization: Basic YWFhcHJveHktamZrLmlvOlBhc3Nmb3JEZXYyMDI1IQ==]
2025-08-26 14:51:17.476 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [200]
2025-08-26 14:51:17.476 [[http-nio-8080-exec-2]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - Result response {"status":true,"data":[{"id":30,"position":10,"shared_port":47110,"port_type":"SocksV5","ip_type":"IPv4","auth_ip_list":"","auth_user_list":"uwfkpi8w:p34536","web_blacklist":"","web_whitelist":"","expired_at":"31/12/2049 12:00 GMT-5","counter_dl_limit":"unlimited","counter_dl_limit_by":"DAILY","counter_dl_quota":100,"counter_dl_bytes":1453620481,"counter_dl_updated_time":1756191276,"counter_dl_reset_time":0,"counter_ul_limit":"unlimited","counter_ul_limit_by":"DAILY","counter_ul_quota":100,"counter_ul_bytes":855468801,"counter_ul_updated_time":1756191276,"counter_ul_reset_time":0,"bw_limit_enabled":false,"bw_limit_rate":1,"memo":"","created_at":"12/07/2025 00:58 GMT-4","custom_dns":"","counter_all_limit":"unlimited","counter_all_limit_by":"DAILY","counter_all_quota":100,"counter_all_bytes":2309089282,"counter_all_updated_time":1756191276,"counter_all_reset_time":0,"connection_limit":1000,"connection_limit_period":60,"api_token":"687ba97e98854962b607b9b6690a36c5","force_server_resolve_dns":false,"interval_rotation":0,"last_rotation":null,"last_rotation_status":false,"allow_api_change_rotation":false,"allow_api_rotate":false,"min_interval_rotation":0,"min_second_rotate":0,"enabled":true,"tcp_os":"linux","gen_method":1,"group_positions":[],"auth_ip_enabled":false,"auth_user_enabled":true,"web_blacklist_enabled":false,"web_whitelist_enabled":false,"expired_at_unix":2524582800000,"remaining":8893.38104896221,"is_expired":false,"remaining_str":"24yr, 4mo, 13d, 9hr, 8min, 42s","counter_dl_limit_quota":100,"counter_dl_used_bytes":1453620481,"counter_dl_used_mb":1386.3,"counter_dl_updated":"26/08 06:54:36","counter_dl_reset":null,"counter_ul_limit_quota":100,"counter_ul_used_bytes":855468801,"counter_ul_used_mb":815.8,"counter_ul_updated":"26/08 06:54:36","counter_ul_reset":null,"counter_all_limit_quota":100,"counter_all_used_bytes":2309089282,"counter_all_used_mb":2202.1,"counter_all_updated":"26/08 06:54:36","counter_all_reset":null,"physical_port_status":"active"}]}

2025-08-26 14:51:17.986 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [http://aaaproxy-jfk.aaaproxy.io:22031//selling/reset_data_counter]
2025-08-26 14:51:17.986 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 14:51:17.986 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Authorization: Basic YWFhcHJveHktamZrLmlvOlBhc3Nmb3JEZXYyMDI1IQ==]
2025-08-26 14:51:18.576 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [200]
2025-08-26 14:51:18.576 [[http-nio-8080-exec-2]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - Result response 1
2025-08-26 14:51:35.738 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:51:35.738 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:51:36.621 [[http-nio-8080-exec-9]] INFO  c.n.p.c.client.ClientProxyController - ------ Change location with location PHILADELPHIA, PA
2025-08-26 14:51:38.816 [[http-nio-8080-exec-9]] INFO  c.n.p.service.impl.ProxyServiceImpl - Total license list 1
2025-08-26 14:51:40.133 [[http-nio-8080-exec-9]] INFO  c.n.p.service.impl.ProxyServiceImpl - Location change fee 1 deducted from user d2a4ceaa-61dd-4cf2-8db0-25490f5d66ab balance for license ad38738e-9f98-4e31-87ab-efb0fbcaf6d3
2025-08-26 14:51:49.853 [[http-nio-8080-exec-9]] INFO  c.n.p.service.impl.ProxyServiceImpl - _____Brother port of http port 32101 with sock port: 37101
2025-08-26 14:51:49.853 [[MyExecutor-3]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - ___ REQUEST: {"positionFrom":1,"positionTo":1,"numberOfPorts":1,"authMethod":0,"authEntry":"","ipAuthenticationEntry":"","userAuthenticationEntry":"u4aj0mw9:p32705","portType":1,"ipType":1,"genPort":2,"genPortStart":30000,"expiredDate":2524582800000,"whitelistLimitAccessEntry":"","blacklistLimitAccessEntry":"","counterUploadLimit":0,"counterUploadLimitBy":1,"counterUploadQuotaInMB":100,"counterDownloadLimit":0,"counterDownloadLimitBy":1,"counterDownloadQuotaInMB":100,"counterAllLimit":0,"counterAllLimitBy":1,"counterAllQuotaInMB":100,"bwLimitEnabled":0,"bwLimitRate":0,"customDNS":"","maxConnection":1000,"memo":"","default_position":true,"default_whitelistLimitAccess":true,"default_blacklistLimitAccess":true,"default_ipAuthentication":false,"default_userAuthentication":false,"default_portType":true,"default_ipType":true,"default_customDNS":true,"default_expiredDate":true,"default_limitAccess":true,"default_bwLimitEnabled":true,"default_counterDownloadLimit":true,"default_counterUploadLimit":true,"default_counterAllLimit":true,"default_maxConnection":false,"default_memo":true,"default_forceServerResolveDns":false,"default_userRotateSetting":false,"ids":[10,30]}
2025-08-26 14:51:49.854 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [http://aaaproxy-jfk.aaaproxy.io:22031//selling/bulk_edit]
2025-08-26 14:51:49.854 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 14:51:49.854 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Authorization: Basic YWFhcHJveHktamZrLmlvOlBhc3Nmb3JEZXYyMDI1IQ==]
2025-08-26 14:51:50.475 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [200]
2025-08-26 14:51:50.475 [[MyExecutor-3]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - Result response {"status":true,"data":[{"id":10,"position":10,"shared_port":42110,"port_type":"HTTP","ip_type":"IPv4","auth_ip_list":"","auth_user_list":"u4aj0mw9:p32705","web_blacklist":"","web_whitelist":"","expired_at":"31/12/2049 12:00 GMT-5","counter_dl_limit":"unlimited","counter_dl_limit_by":"DAILY","counter_dl_quota":100,"counter_dl_bytes":0,"counter_dl_updated_time":0,"counter_dl_reset_time":0,"counter_ul_limit":"unlimited","counter_ul_limit_by":"DAILY","counter_ul_quota":100,"counter_ul_bytes":0,"counter_ul_updated_time":0,"counter_ul_reset_time":0,"bw_limit_enabled":false,"bw_limit_rate":1,"memo":"","created_at":"12/07/2025 00:58 GMT-4","custom_dns":"","counter_all_limit":"unlimited","counter_all_limit_by":"DAILY","counter_all_quota":100,"counter_all_bytes":0,"counter_all_updated_time":0,"counter_all_reset_time":0,"connection_limit":1000,"connection_limit_period":60,"api_token":"ca61abb1735f42ae8c9ff6eb057b03b6","force_server_resolve_dns":false,"interval_rotation":0,"last_rotation":null,"last_rotation_status":false,"allow_api_change_rotation":false,"allow_api_rotate":false,"min_interval_rotation":0,"min_second_rotate":0,"enabled":true,"tcp_os":"linux","gen_method":1,"group_positions":[],"auth_ip_enabled":false,"auth_user_enabled":true,"web_blacklist_enabled":false,"web_whitelist_enabled":false,"expired_at_unix":2524582800000,"remaining":8893.380666840567,"is_expired":false,"remaining_str":"24yr, 4mo, 13d, 9hr, 8min, 9s","counter_dl_limit_quota":100,"counter_dl_used_bytes":0,"counter_dl_used_mb":0.0,"counter_dl_updated":null,"counter_dl_reset":null,"counter_ul_limit_quota":100,"counter_ul_used_bytes":0,"counter_ul_used_mb":0.0,"counter_ul_updated":null,"counter_ul_reset":null,"counter_all_limit_quota":100,"counter_all_used_bytes":0,"counter_all_used_mb":0.0,"counter_all_updated":null,"counter_all_reset":null,"physical_port_status":"active"},{"id":30,"position":10,"shared_port":47110,"port_type":"SocksV5","ip_type":"IPv4","auth_ip_list":"","auth_user_list":"u4aj0mw9:p32705","web_blacklist":"","web_whitelist":"","expired_at":"31/12/2049 12:00 GMT-5","counter_dl_limit":"unlimited","counter_dl_limit_by":"DAILY","counter_dl_quota":100,"counter_dl_bytes":0,"counter_dl_updated_time":0,"counter_dl_reset_time":0,"counter_ul_limit":"unlimited","counter_ul_limit_by":"DAILY","counter_ul_quota":100,"counter_ul_bytes":0,"counter_ul_updated_time":0,"counter_ul_reset_time":0,"bw_limit_enabled":false,"bw_limit_rate":1,"memo":"","created_at":"12/07/2025 00:58 GMT-4","custom_dns":"","counter_all_limit":"unlimited","counter_all_limit_by":"DAILY","counter_all_quota":100,"counter_all_bytes":0,"counter_all_updated_time":0,"counter_all_reset_time":0,"connection_limit":1000,"connection_limit_period":60,"api_token":"687ba97e98854962b607b9b6690a36c5","force_server_resolve_dns":false,"interval_rotation":0,"last_rotation":null,"last_rotation_status":false,"allow_api_change_rotation":false,"allow_api_rotate":false,"min_interval_rotation":0,"min_second_rotate":0,"enabled":true,"tcp_os":"linux","gen_method":1,"group_positions":[],"auth_ip_enabled":false,"auth_user_enabled":true,"web_blacklist_enabled":false,"web_whitelist_enabled":false,"expired_at_unix":2524582800000,"remaining":8893.380666839179,"is_expired":false,"remaining_str":"24yr, 4mo, 13d, 9hr, 8min, 9s","counter_dl_limit_quota":100,"counter_dl_used_bytes":0,"counter_dl_used_mb":0.0,"counter_dl_updated":null,"counter_dl_reset":null,"counter_ul_limit_quota":100,"counter_ul_used_bytes":0,"counter_ul_used_mb":0.0,"counter_ul_updated":null,"counter_ul_reset":null,"counter_all_limit_quota":100,"counter_all_used_bytes":0,"counter_all_used_mb":0.0,"counter_all_updated":null,"counter_all_reset":null,"physical_port_status":"active"}]}

2025-08-26 14:51:52.011 [[http-nio-8080-exec-9]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - ___ REQUEST: {"positionFrom":1,"positionTo":1,"numberOfPorts":1,"authMethod":0,"authEntry":"","ipAuthenticationEntry":"","userAuthenticationEntry":"uwfkpi8w:p34536","portType":1,"ipType":1,"genPort":2,"genPortStart":30000,"expiredDate":2524582800000,"whitelistLimitAccessEntry":"","blacklistLimitAccessEntry":"","counterUploadLimit":0,"counterUploadLimitBy":1,"counterUploadQuotaInMB":100,"counterDownloadLimit":0,"counterDownloadLimitBy":1,"counterDownloadQuotaInMB":100,"counterAllLimit":0,"counterAllLimitBy":1,"counterAllQuotaInMB":100,"bwLimitEnabled":0,"bwLimitRate":0,"customDNS":"","maxConnection":1000,"memo":"","default_position":true,"default_whitelistLimitAccess":true,"default_blacklistLimitAccess":true,"default_ipAuthentication":false,"default_userAuthentication":false,"default_portType":true,"default_ipType":true,"default_customDNS":true,"default_expiredDate":true,"default_limitAccess":true,"default_bwLimitEnabled":true,"default_counterDownloadLimit":true,"default_counterUploadLimit":true,"default_counterAllLimit":true,"default_maxConnection":false,"default_memo":true,"default_forceServerResolveDns":false,"default_userRotateSetting":false,"ids":[73]}
2025-08-26 14:51:52.011 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [http://aaaproxy-phl.aaaproxy.io:22021//selling/bulk_edit]
2025-08-26 14:51:52.011 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 14:51:52.011 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Authorization: Basic YWFhcHJveHktcGEuaW86UGFzc2ZvckRldjIwMjUh]
2025-08-26 14:51:52.677 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [200]
2025-08-26 14:51:52.677 [[http-nio-8080-exec-9]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - Result response {"status":true,"data":[{"id":73,"position":1,"shared_port":32101,"port_type":"HTTP","ip_type":"IPv46","auth_ip_list":"","auth_user_list":"uwfkpi8w:p34536","web_blacklist":"","web_whitelist":"","expired_at":"31/12/2049 12:00 GMT-5","counter_dl_limit":"unlimited","counter_dl_limit_by":"DAILY","counter_dl_quota":100,"counter_dl_bytes":0,"counter_dl_updated_time":0,"counter_dl_reset_time":0,"counter_ul_limit":"unlimited","counter_ul_limit_by":"DAILY","counter_ul_quota":100,"counter_ul_bytes":0,"counter_ul_updated_time":0,"counter_ul_reset_time":0,"bw_limit_enabled":false,"bw_limit_rate":1,"memo":"","created_at":"11/07/2025 22:02 GMT-4","custom_dns":"","counter_all_limit":"unlimited","counter_all_limit_by":"DAILY","counter_all_quota":100,"counter_all_bytes":0,"counter_all_updated_time":0,"counter_all_reset_time":0,"connection_limit":1000,"connection_limit_period":60,"api_token":"01a8761e63e94a6fb36774241fdb0d08","force_server_resolve_dns":false,"interval_rotation":0,"last_rotation":null,"last_rotation_status":false,"allow_api_change_rotation":false,"allow_api_rotate":false,"min_interval_rotation":0,"min_second_rotate":0,"enabled":true,"tcp_os":"ipad1","gen_method":1,"group_positions":[],"auth_ip_enabled":false,"auth_user_enabled":true,"web_blacklist_enabled":false,"web_whitelist_enabled":false,"expired_at_unix":2524582800000,"remaining":8893.380641952084,"is_expired":false,"remaining_str":"24yr, 4mo, 13d, 9hr, 8min, 7s","counter_dl_limit_quota":100,"counter_dl_used_bytes":0,"counter_dl_used_mb":0.0,"counter_dl_updated":null,"counter_dl_reset":null,"counter_ul_limit_quota":100,"counter_ul_used_bytes":0,"counter_ul_used_mb":0.0,"counter_ul_updated":null,"counter_ul_reset":null,"counter_all_limit_quota":100,"counter_all_used_bytes":0,"counter_all_used_mb":0.0,"counter_all_updated":null,"counter_all_reset":null,"physical_port_status":"active"}]}

2025-08-26 14:51:53.996 [[http-nio-8080-exec-9]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - ___ REQUEST: {"positionFrom":1,"positionTo":1,"numberOfPorts":1,"authMethod":0,"authEntry":"","ipAuthenticationEntry":"","userAuthenticationEntry":"uwfkpi8w:p34536","portType":1,"ipType":1,"genPort":2,"genPortStart":30000,"expiredDate":2524582800000,"whitelistLimitAccessEntry":"","blacklistLimitAccessEntry":"","counterUploadLimit":0,"counterUploadLimitBy":1,"counterUploadQuotaInMB":100,"counterDownloadLimit":0,"counterDownloadLimitBy":1,"counterDownloadQuotaInMB":100,"counterAllLimit":0,"counterAllLimitBy":1,"counterAllQuotaInMB":100,"bwLimitEnabled":0,"bwLimitRate":0,"customDNS":"","maxConnection":1000,"memo":"","default_position":true,"default_whitelistLimitAccess":true,"default_blacklistLimitAccess":true,"default_ipAuthentication":false,"default_userAuthentication":false,"default_portType":true,"default_ipType":true,"default_customDNS":true,"default_expiredDate":true,"default_limitAccess":true,"default_bwLimitEnabled":true,"default_counterDownloadLimit":true,"default_counterUploadLimit":true,"default_counterAllLimit":true,"default_maxConnection":false,"default_memo":true,"default_forceServerResolveDns":false,"default_userRotateSetting":false,"ids":[93]}
2025-08-26 14:51:53.997 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [http://aaaproxy-phl.aaaproxy.io:22021//selling/bulk_edit]
2025-08-26 14:51:53.997 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 14:51:53.997 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Authorization: Basic YWFhcHJveHktcGEuaW86UGFzc2ZvckRldjIwMjUh]
2025-08-26 14:51:54.595 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [200]
2025-08-26 14:51:54.595 [[http-nio-8080-exec-9]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - Result response {"status":true,"data":[{"id":93,"position":1,"shared_port":37101,"port_type":"SocksV5","ip_type":"IPv46","auth_ip_list":"","auth_user_list":"uwfkpi8w:p34536","web_blacklist":"","web_whitelist":"","expired_at":"31/12/2049 12:00 GMT-5","counter_dl_limit":"unlimited","counter_dl_limit_by":"DAILY","counter_dl_quota":100,"counter_dl_bytes":0,"counter_dl_updated_time":0,"counter_dl_reset_time":0,"counter_ul_limit":"unlimited","counter_ul_limit_by":"DAILY","counter_ul_quota":100,"counter_ul_bytes":0,"counter_ul_updated_time":0,"counter_ul_reset_time":0,"bw_limit_enabled":false,"bw_limit_rate":1,"memo":"","created_at":"11/07/2025 22:02 GMT-4","custom_dns":"","counter_all_limit":"unlimited","counter_all_limit_by":"DAILY","counter_all_quota":100,"counter_all_bytes":0,"counter_all_updated_time":0,"counter_all_reset_time":0,"connection_limit":1000,"connection_limit_period":60,"api_token":"d35fe48f8a184244b40a92d2182f548f","force_server_resolve_dns":false,"interval_rotation":0,"last_rotation":null,"last_rotation_status":false,"allow_api_change_rotation":false,"allow_api_rotate":false,"min_interval_rotation":0,"min_second_rotate":0,"enabled":true,"tcp_os":"ipad1","gen_method":1,"group_positions":[],"auth_ip_enabled":false,"auth_user_enabled":true,"web_blacklist_enabled":false,"web_whitelist_enabled":false,"expired_at_unix":2524582800000,"remaining":8893.380619663045,"is_expired":false,"remaining_str":"24yr, 4mo, 13d, 9hr, 8min, 5s","counter_dl_limit_quota":100,"counter_dl_used_bytes":0,"counter_dl_used_mb":0.0,"counter_dl_updated":null,"counter_dl_reset":null,"counter_ul_limit_quota":100,"counter_ul_used_bytes":0,"counter_ul_used_mb":0.0,"counter_ul_updated":null,"counter_ul_reset":null,"counter_all_limit_quota":100,"counter_all_used_bytes":0,"counter_all_used_mb":0.0,"counter_all_updated":null,"counter_all_reset":null,"physical_port_status":"active"}]}

2025-08-26 14:51:55.185 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [http://aaaproxy-phl.aaaproxy.io:22021//selling/reset_data_counter]
2025-08-26 14:51:55.185 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 14:51:55.185 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Authorization: Basic YWFhcHJveHktcGEuaW86UGFzc2ZvckRldjIwMjUh]
2025-08-26 14:51:55.797 [[ForkJoinPool.commonPool-worker-2]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [200]
2025-08-26 14:51:55.797 [[http-nio-8080-exec-9]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - Result response 1
2025-08-26 14:52:05.738 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:52:05.738 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:52:35.739 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:52:35.739 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:53:05.739 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:53:05.739 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:53:35.740 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:53:35.740 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:54:05.741 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:54:05.741 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:54:35.741 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:54:35.742 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:55:05.742 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:55:05.742 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:55:35.742 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:55:35.742 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:56:05.743 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:56:05.743 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:56:35.744 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:56:35.744 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:57:05.745 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:57:05.745 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:57:35.745 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:57:35.745 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:58:05.746 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:58:05.746 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:58:21.135 [[http-nio-8080-exec-3]] INFO  c.n.p.s.impl.LicenseServiceImpl - 2025-08-26 - 2025-08-27T00:00
2025-08-26 14:58:21.135 [[http-nio-8080-exec-7]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [https://api-sandbox.nowpayments.io/v1/merchant/coins]
2025-08-26 14:58:21.135 [[http-nio-8080-exec-7]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 14:58:21.135 [[http-nio-8080-exec-7]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [x-api-key: H3VKFJT-8VSM715-QRWG6JH-1MMACJW]
2025-08-26 14:58:22.074 [[http-nio-8080-exec-7]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [403]
2025-08-26 14:58:22.074 [[http-nio-8080-exec-7]] INFO  c.n.pro.utils.httpClient.ApiClient - Error message: [{"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}]
2025-08-26 14:58:22.074 [[http-nio-8080-exec-7]] INFO  c.n.pro.utils.httpClient.ApiClient - Status NOT OK. throw Error code: [400]
2025-08-26 14:58:22.076 [[http-nio-8080-exec-7]] ERROR c.n.pro.utils.httpClient.ApiClient -  CALL PARTNER [Error] https://api-sandbox.nowpayments.io/v1/merchant/coins
java.lang.Exception: 400 Error when map code: 403
	at com.neoproxy.pro.utils.httpClient.ApiClient.invoke(ApiClient.java:91)
	at com.neoproxy.pro.utils.httpClient.ApiClient.callAPI(ApiClient.java:61)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getSelectedCurrencies(NowPaymentServiceImpl.java:58)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getFullCurrencies(NowPaymentServiceImpl.java:72)
	at com.neoproxy.pro.controller.client.PaymentController.getFullCurrencies(PaymentController.java:46)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.neoproxy.pro.config.JwtRequestFilter.doFilterInternal(JwtRequestFilter.java:33)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1732)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-26 14:58:22.076 [[http-nio-8080-exec-7]] ERROR c.n.p.n.s.impl.NowPaymentServiceImpl - [NowPayment] Error: 400
2025-08-26 14:58:22.350 [[http-nio-8080-exec-7]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [https://api-sandbox.nowpayments.io/v1/full-currencies]
2025-08-26 14:58:22.350 [[http-nio-8080-exec-7]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 14:58:22.350 [[http-nio-8080-exec-7]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [x-api-key: H3VKFJT-8VSM715-QRWG6JH-1MMACJW]
2025-08-26 14:58:22.589 [[http-nio-8080-exec-7]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [403]
2025-08-26 14:58:22.589 [[http-nio-8080-exec-7]] INFO  c.n.pro.utils.httpClient.ApiClient - Error message: [{"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}]
2025-08-26 14:58:22.589 [[http-nio-8080-exec-7]] INFO  c.n.pro.utils.httpClient.ApiClient - Status NOT OK. throw Error code: [400]
2025-08-26 14:58:22.589 [[http-nio-8080-exec-7]] ERROR c.n.pro.utils.httpClient.ApiClient -  CALL PARTNER [Error] https://api-sandbox.nowpayments.io/v1/full-currencies
java.lang.Exception: 400 Error when map code: 403
	at com.neoproxy.pro.utils.httpClient.ApiClient.invoke(ApiClient.java:91)
	at com.neoproxy.pro.utils.httpClient.ApiClient.callAPI(ApiClient.java:61)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getFullCurrencies(NowPaymentServiceImpl.java:73)
	at com.neoproxy.pro.controller.client.PaymentController.getFullCurrencies(PaymentController.java:46)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.neoproxy.pro.config.JwtRequestFilter.doFilterInternal(JwtRequestFilter.java:33)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1732)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-26 14:58:22.590 [[http-nio-8080-exec-7]] ERROR c.n.p.n.s.impl.NowPaymentServiceImpl - [NowPayment] Error: 400
2025-08-26 14:58:35.747 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 14:58:35.747 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:59:05.747 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=2, idle=8, waiting=0)
2025-08-26 14:59:05.747 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 14:59:35.748 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 14:59:35.748 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:00:05.748 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:00:05.748 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:00:14.979 [[http-nio-8080-exec-4]] INFO  c.n.p.c.client.ClientProxyController - ------ Change location with location NEW YORK, NY
2025-08-26 15:00:17.208 [[http-nio-8080-exec-4]] INFO  c.n.p.service.impl.ProxyServiceImpl - Total license list 1
2025-08-26 15:00:18.588 [[http-nio-8080-exec-4]] INFO  c.n.p.service.impl.ProxyServiceImpl - Location change fee 1 deducted from user d2a4ceaa-61dd-4cf2-8db0-25490f5d66ab balance for license ad38738e-9f98-4e31-87ab-efb0fbcaf6d3
2025-08-26 15:00:31.671 [[http-nio-8080-exec-4]] INFO  c.n.p.service.impl.ProxyServiceImpl - _____Brother port of http port 42119 with sock port: 47119
2025-08-26 15:00:31.671 [[MyExecutor-4]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - ___ REQUEST: {"positionFrom":1,"positionTo":1,"numberOfPorts":1,"authMethod":0,"authEntry":"","ipAuthenticationEntry":"","userAuthenticationEntry":"um0fymv9:p11157","portType":1,"ipType":1,"genPort":2,"genPortStart":30000,"expiredDate":2524582800000,"whitelistLimitAccessEntry":"","blacklistLimitAccessEntry":"","counterUploadLimit":0,"counterUploadLimitBy":1,"counterUploadQuotaInMB":100,"counterDownloadLimit":0,"counterDownloadLimitBy":1,"counterDownloadQuotaInMB":100,"counterAllLimit":0,"counterAllLimitBy":1,"counterAllQuotaInMB":100,"bwLimitEnabled":0,"bwLimitRate":0,"customDNS":"","maxConnection":1000,"memo":"","default_position":true,"default_whitelistLimitAccess":true,"default_blacklistLimitAccess":true,"default_ipAuthentication":false,"default_userAuthentication":false,"default_portType":true,"default_ipType":true,"default_customDNS":true,"default_expiredDate":true,"default_limitAccess":true,"default_bwLimitEnabled":true,"default_counterDownloadLimit":true,"default_counterUploadLimit":true,"default_counterAllLimit":true,"default_maxConnection":false,"default_memo":true,"default_forceServerResolveDns":false,"default_userRotateSetting":false,"ids":[73,93]}
2025-08-26 15:00:31.672 [[ForkJoinPool.commonPool-worker-3]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [http://aaaproxy-phl.aaaproxy.io:22021//selling/bulk_edit]
2025-08-26 15:00:31.672 [[ForkJoinPool.commonPool-worker-3]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 15:00:31.672 [[ForkJoinPool.commonPool-worker-3]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Authorization: Basic YWFhcHJveHktcGEuaW86UGFzc2ZvckRldjIwMjUh]
2025-08-26 15:00:32.552 [[ForkJoinPool.commonPool-worker-3]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [200]
2025-08-26 15:00:32.552 [[MyExecutor-4]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - Result response {"status":true,"data":[{"id":73,"position":1,"shared_port":32101,"port_type":"HTTP","ip_type":"IPv46","auth_ip_list":"","auth_user_list":"um0fymv9:p11157","web_blacklist":"","web_whitelist":"","expired_at":"31/12/2049 12:00 GMT-5","counter_dl_limit":"unlimited","counter_dl_limit_by":"DAILY","counter_dl_quota":100,"counter_dl_bytes":0,"counter_dl_updated_time":0,"counter_dl_reset_time":0,"counter_ul_limit":"unlimited","counter_ul_limit_by":"DAILY","counter_ul_quota":100,"counter_ul_bytes":0,"counter_ul_updated_time":0,"counter_ul_reset_time":0,"bw_limit_enabled":false,"bw_limit_rate":1,"memo":"","created_at":"11/07/2025 22:02 GMT-4","custom_dns":"","counter_all_limit":"unlimited","counter_all_limit_by":"DAILY","counter_all_quota":100,"counter_all_bytes":0,"counter_all_updated_time":0,"counter_all_reset_time":0,"connection_limit":1000,"connection_limit_period":60,"api_token":"01a8761e63e94a6fb36774241fdb0d08","force_server_resolve_dns":false,"interval_rotation":0,"last_rotation":null,"last_rotation_status":false,"allow_api_change_rotation":false,"allow_api_rotate":false,"min_interval_rotation":0,"min_second_rotate":0,"enabled":true,"tcp_os":"ipad1","gen_method":1,"group_positions":[],"auth_ip_enabled":false,"auth_user_enabled":true,"web_blacklist_enabled":false,"web_whitelist_enabled":false,"expired_at_unix":2524582800000,"remaining":8893.37462525706,"is_expired":false,"remaining_str":"24yr, 4mo, 13d, 8hr, 59min, 27s","counter_dl_limit_quota":100,"counter_dl_used_bytes":0,"counter_dl_used_mb":0.0,"counter_dl_updated":null,"counter_dl_reset":null,"counter_ul_limit_quota":100,"counter_ul_used_bytes":0,"counter_ul_used_mb":0.0,"counter_ul_updated":null,"counter_ul_reset":null,"counter_all_limit_quota":100,"counter_all_used_bytes":0,"counter_all_used_mb":0.0,"counter_all_updated":null,"counter_all_reset":null,"physical_port_status":"active"},{"id":93,"position":1,"shared_port":37101,"port_type":"SocksV5","ip_type":"IPv46","auth_ip_list":"","auth_user_list":"um0fymv9:p11157","web_blacklist":"","web_whitelist":"","expired_at":"31/12/2049 12:00 GMT-5","counter_dl_limit":"unlimited","counter_dl_limit_by":"DAILY","counter_dl_quota":100,"counter_dl_bytes":0,"counter_dl_updated_time":0,"counter_dl_reset_time":0,"counter_ul_limit":"unlimited","counter_ul_limit_by":"DAILY","counter_ul_quota":100,"counter_ul_bytes":0,"counter_ul_updated_time":0,"counter_ul_reset_time":0,"bw_limit_enabled":false,"bw_limit_rate":1,"memo":"","created_at":"11/07/2025 22:02 GMT-4","custom_dns":"","counter_all_limit":"unlimited","counter_all_limit_by":"DAILY","counter_all_quota":100,"counter_all_bytes":0,"counter_all_updated_time":0,"counter_all_reset_time":0,"connection_limit":1000,"connection_limit_period":60,"api_token":"d35fe48f8a184244b40a92d2182f548f","force_server_resolve_dns":false,"interval_rotation":0,"last_rotation":null,"last_rotation_status":false,"allow_api_change_rotation":false,"allow_api_rotate":false,"min_interval_rotation":0,"min_second_rotate":0,"enabled":true,"tcp_os":"ipad1","gen_method":1,"group_positions":[],"auth_ip_enabled":false,"auth_user_enabled":true,"web_blacklist_enabled":false,"web_whitelist_enabled":false,"expired_at_unix":2524582800000,"remaining":8893.374625254226,"is_expired":false,"remaining_str":"24yr, 4mo, 13d, 8hr, 59min, 27s","counter_dl_limit_quota":100,"counter_dl_used_bytes":0,"counter_dl_used_mb":0.0,"counter_dl_updated":null,"counter_dl_reset":null,"counter_ul_limit_quota":100,"counter_ul_used_bytes":0,"counter_ul_used_mb":0.0,"counter_ul_updated":null,"counter_ul_reset":null,"counter_all_limit_quota":100,"counter_all_used_bytes":0,"counter_all_used_mb":0.0,"counter_all_updated":null,"counter_all_reset":null,"physical_port_status":"active"}]}

2025-08-26 15:00:33.872 [[http-nio-8080-exec-4]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - ___ REQUEST: {"positionFrom":1,"positionTo":1,"numberOfPorts":1,"authMethod":0,"authEntry":"","ipAuthenticationEntry":"","userAuthenticationEntry":"uwfkpi8w:p34536","portType":1,"ipType":1,"genPort":2,"genPortStart":30000,"expiredDate":2524582800000,"whitelistLimitAccessEntry":"","blacklistLimitAccessEntry":"","counterUploadLimit":0,"counterUploadLimitBy":1,"counterUploadQuotaInMB":100,"counterDownloadLimit":0,"counterDownloadLimitBy":1,"counterDownloadQuotaInMB":100,"counterAllLimit":0,"counterAllLimitBy":1,"counterAllQuotaInMB":100,"bwLimitEnabled":0,"bwLimitRate":0,"customDNS":"","maxConnection":1000,"memo":"","default_position":true,"default_whitelistLimitAccess":true,"default_blacklistLimitAccess":true,"default_ipAuthentication":false,"default_userAuthentication":false,"default_portType":true,"default_ipType":true,"default_customDNS":true,"default_expiredDate":true,"default_limitAccess":true,"default_bwLimitEnabled":true,"default_counterDownloadLimit":true,"default_counterUploadLimit":true,"default_counterAllLimit":true,"default_maxConnection":false,"default_memo":true,"default_forceServerResolveDns":false,"default_userRotateSetting":false,"ids":[19]}
2025-08-26 15:00:33.873 [[ForkJoinPool.commonPool-worker-3]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [http://aaaproxy-jfk.aaaproxy.io:22031//selling/bulk_edit]
2025-08-26 15:00:33.873 [[ForkJoinPool.commonPool-worker-3]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 15:00:33.873 [[ForkJoinPool.commonPool-worker-3]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Authorization: Basic YWFhcHJveHktamZrLmlvOlBhc3Nmb3JEZXYyMDI1IQ==]
2025-08-26 15:00:34.635 [[ForkJoinPool.commonPool-worker-3]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [200]
2025-08-26 15:00:34.635 [[http-nio-8080-exec-4]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - Result response {"status":true,"data":[{"id":19,"position":19,"shared_port":42119,"port_type":"HTTP","ip_type":"IPv4","auth_ip_list":"","auth_user_list":"uwfkpi8w:p34536","web_blacklist":"","web_whitelist":"","expired_at":"31/12/2049 12:00 GMT-5","counter_dl_limit":"unlimited","counter_dl_limit_by":"DAILY","counter_dl_quota":100,"counter_dl_bytes":501871254631,"counter_dl_updated_time":1756052097,"counter_dl_reset_time":0,"counter_ul_limit":"unlimited","counter_ul_limit_by":"DAILY","counter_ul_quota":100,"counter_ul_bytes":35107186235,"counter_ul_updated_time":1756052097,"counter_ul_reset_time":0,"bw_limit_enabled":false,"bw_limit_rate":1,"memo":"","created_at":"12/07/2025 00:58 GMT-4","custom_dns":"","counter_all_limit":"unlimited","counter_all_limit_by":"DAILY","counter_all_quota":100,"counter_all_bytes":536978440866,"counter_all_updated_time":1756052097,"counter_all_reset_time":0,"connection_limit":1000,"connection_limit_period":60,"api_token":"19414a2f8ef0441b84a0f490c8066e93","force_server_resolve_dns":false,"interval_rotation":0,"last_rotation":null,"last_rotation_status":false,"allow_api_change_rotation":false,"allow_api_rotate":false,"min_interval_rotation":0,"min_second_rotate":0,"enabled":true,"tcp_os":"linux","gen_method":1,"group_positions":[],"auth_ip_enabled":false,"auth_user_enabled":true,"web_blacklist_enabled":false,"web_whitelist_enabled":false,"expired_at_unix":2524582800000,"remaining":8893.374600240462,"is_expired":false,"remaining_str":"24yr, 4mo, 13d, 8hr, 59min, 25s","counter_dl_limit_quota":100,"counter_dl_used_bytes":501871254631,"counter_dl_used_mb":478621.7,"counter_dl_updated":"24/08 16:14:57","counter_dl_reset":null,"counter_ul_limit_quota":100,"counter_ul_used_bytes":35107186235,"counter_ul_used_mb":33480.8,"counter_ul_updated":"24/08 16:14:57","counter_ul_reset":null,"counter_all_limit_quota":100,"counter_all_used_bytes":536978440866,"counter_all_used_mb":512102.5,"counter_all_updated":"24/08 16:14:57","counter_all_reset":null,"physical_port_status":"active"}]}

2025-08-26 15:00:35.748 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-26 15:00:35.748 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:00:35.854 [[http-nio-8080-exec-4]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - ___ REQUEST: {"positionFrom":1,"positionTo":1,"numberOfPorts":1,"authMethod":0,"authEntry":"","ipAuthenticationEntry":"","userAuthenticationEntry":"uwfkpi8w:p34536","portType":1,"ipType":1,"genPort":2,"genPortStart":30000,"expiredDate":2524582800000,"whitelistLimitAccessEntry":"","blacklistLimitAccessEntry":"","counterUploadLimit":0,"counterUploadLimitBy":1,"counterUploadQuotaInMB":100,"counterDownloadLimit":0,"counterDownloadLimitBy":1,"counterDownloadQuotaInMB":100,"counterAllLimit":0,"counterAllLimitBy":1,"counterAllQuotaInMB":100,"bwLimitEnabled":0,"bwLimitRate":0,"customDNS":"","maxConnection":1000,"memo":"","default_position":true,"default_whitelistLimitAccess":true,"default_blacklistLimitAccess":true,"default_ipAuthentication":false,"default_userAuthentication":false,"default_portType":true,"default_ipType":true,"default_customDNS":true,"default_expiredDate":true,"default_limitAccess":true,"default_bwLimitEnabled":true,"default_counterDownloadLimit":true,"default_counterUploadLimit":true,"default_counterAllLimit":true,"default_maxConnection":false,"default_memo":true,"default_forceServerResolveDns":false,"default_userRotateSetting":false,"ids":[39]}
2025-08-26 15:00:35.854 [[ForkJoinPool.commonPool-worker-3]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [http://aaaproxy-jfk.aaaproxy.io:22031//selling/bulk_edit]
2025-08-26 15:00:35.854 [[ForkJoinPool.commonPool-worker-3]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 15:00:35.854 [[ForkJoinPool.commonPool-worker-3]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Authorization: Basic YWFhcHJveHktamZrLmlvOlBhc3Nmb3JEZXYyMDI1IQ==]
2025-08-26 15:00:36.394 [[ForkJoinPool.commonPool-worker-3]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [200]
2025-08-26 15:00:36.394 [[http-nio-8080-exec-4]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - Result response {"status":true,"data":[{"id":39,"position":19,"shared_port":47119,"port_type":"SocksV5","ip_type":"IPv4","auth_ip_list":"","auth_user_list":"uwfkpi8w:p34536","web_blacklist":"","web_whitelist":"","expired_at":"31/12/2049 12:00 GMT-5","counter_dl_limit":"unlimited","counter_dl_limit_by":"DAILY","counter_dl_quota":100,"counter_dl_bytes":252527452372,"counter_dl_updated_time":1756052036,"counter_dl_reset_time":0,"counter_ul_limit":"unlimited","counter_ul_limit_by":"DAILY","counter_ul_quota":100,"counter_ul_bytes":18032697527,"counter_ul_updated_time":1756052036,"counter_ul_reset_time":0,"bw_limit_enabled":false,"bw_limit_rate":1,"memo":"","created_at":"12/07/2025 00:58 GMT-4","custom_dns":"","counter_all_limit":"unlimited","counter_all_limit_by":"DAILY","counter_all_quota":100,"counter_all_bytes":270560149899,"counter_all_updated_time":1756052036,"counter_all_reset_time":0,"connection_limit":1000,"connection_limit_period":60,"api_token":"a7a0580039eb4464a4c6a40602d740bd","force_server_resolve_dns":false,"interval_rotation":0,"last_rotation":null,"last_rotation_status":false,"allow_api_change_rotation":false,"allow_api_rotate":false,"min_interval_rotation":0,"min_second_rotate":0,"enabled":true,"tcp_os":"linux","gen_method":1,"group_positions":[],"auth_ip_enabled":false,"auth_user_enabled":true,"web_blacklist_enabled":false,"web_whitelist_enabled":false,"expired_at_unix":2524582800000,"remaining":8893.374579942361,"is_expired":false,"remaining_str":"24yr, 4mo, 13d, 8hr, 59min, 23s","counter_dl_limit_quota":100,"counter_dl_used_bytes":252527452372,"counter_dl_used_mb":240828.9,"counter_dl_updated":"24/08 16:13:56","counter_dl_reset":null,"counter_ul_limit_quota":100,"counter_ul_used_bytes":18032697527,"counter_ul_used_mb":17197.3,"counter_ul_updated":"24/08 16:13:56","counter_ul_reset":null,"counter_all_limit_quota":100,"counter_all_used_bytes":270560149899,"counter_all_used_mb":258026.3,"counter_all_updated":"24/08 16:13:56","counter_all_reset":null,"physical_port_status":"active"}]}

2025-08-26 15:00:37.030 [[ForkJoinPool.commonPool-worker-3]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [http://aaaproxy-jfk.aaaproxy.io:22031//selling/reset_data_counter]
2025-08-26 15:00:37.030 [[ForkJoinPool.commonPool-worker-3]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 15:00:37.030 [[ForkJoinPool.commonPool-worker-3]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Authorization: Basic YWFhcHJveHktamZrLmlvOlBhc3Nmb3JEZXYyMDI1IQ==]
2025-08-26 15:00:37.613 [[ForkJoinPool.commonPool-worker-3]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [200]
2025-08-26 15:00:37.613 [[http-nio-8080-exec-4]] INFO  c.n.p.x.s.impl.XProxyServiceImpl - Result response 1
2025-08-26 15:01:05.372 [[http-nio-8080-exec-9]] ERROR c.n.p.s.a.j.JwtAuthenticationServiceImpl - Error when extract jwt token: {}
io.jsonwebtoken.ExpiredJwtException: JWT expired at 2025-08-26T15:00:56Z. Current time: 2025-08-26T15:01:05Z, a difference of 9371 milliseconds.  Allowed clock skew: 0 milliseconds.
	at io.jsonwebtoken.impl.DefaultJwtParser.parse(DefaultJwtParser.java:385)
	at io.jsonwebtoken.impl.DefaultJwtParser.parse(DefaultJwtParser.java:481)
	at io.jsonwebtoken.impl.DefaultJwtParser.parseClaimsJws(DefaultJwtParser.java:541)
	at com.neoproxy.pro.utils.JwtTokenUtil.getAllClaimsFromToken(JwtTokenUtil.java:45)
	at com.neoproxy.pro.utils.JwtTokenUtil.getClaimFromToken(JwtTokenUtil.java:40)
	at com.neoproxy.pro.utils.JwtTokenUtil.getSubjectFromToken(JwtTokenUtil.java:31)
	at com.neoproxy.pro.service.authentication.jwt.JwtAuthenticationServiceImpl.authenticate(JwtAuthenticationServiceImpl.java:44)
	at com.neoproxy.pro.config.JwtRequestFilter.doFilterInternal(JwtRequestFilter.java:31)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1732)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-26 15:01:05.373 [[http-nio-8080-exec-9]] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [/neoproxy] threw exception
com.neoproxy.pro.service.exception.NeoProxyServiceException: JWT Token has expired
	at com.neoproxy.pro.service.authentication.jwt.JwtAuthenticationServiceImpl.authenticate(JwtAuthenticationServiceImpl.java:65)
	at com.neoproxy.pro.config.JwtRequestFilter.doFilterInternal(JwtRequestFilter.java:31)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1732)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-26 15:01:05.749 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:01:05.749 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:01:09.332 [[http-nio-8080-exec-8]] INFO  c.n.p.s.impl.LicenseServiceImpl - 2025-08-26 - 2025-08-27T00:00
2025-08-26 15:01:09.344 [[http-nio-8080-exec-4]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [https://api-sandbox.nowpayments.io/v1/merchant/coins]
2025-08-26 15:01:09.344 [[http-nio-8080-exec-4]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 15:01:09.344 [[http-nio-8080-exec-4]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [x-api-key: H3VKFJT-8VSM715-QRWG6JH-1MMACJW]
2025-08-26 15:01:09.591 [[http-nio-8080-exec-4]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [403]
2025-08-26 15:01:09.591 [[http-nio-8080-exec-4]] INFO  c.n.pro.utils.httpClient.ApiClient - Error message: [{"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}]
2025-08-26 15:01:09.591 [[http-nio-8080-exec-4]] INFO  c.n.pro.utils.httpClient.ApiClient - Status NOT OK. throw Error code: [400]
2025-08-26 15:01:09.591 [[http-nio-8080-exec-4]] ERROR c.n.pro.utils.httpClient.ApiClient -  CALL PARTNER [Error] https://api-sandbox.nowpayments.io/v1/merchant/coins
java.lang.Exception: 400 Error when map code: 403
	at com.neoproxy.pro.utils.httpClient.ApiClient.invoke(ApiClient.java:91)
	at com.neoproxy.pro.utils.httpClient.ApiClient.callAPI(ApiClient.java:61)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getSelectedCurrencies(NowPaymentServiceImpl.java:58)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getFullCurrencies(NowPaymentServiceImpl.java:72)
	at com.neoproxy.pro.controller.client.PaymentController.getFullCurrencies(PaymentController.java:46)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.neoproxy.pro.config.JwtRequestFilter.doFilterInternal(JwtRequestFilter.java:33)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1732)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-26 15:01:09.591 [[http-nio-8080-exec-4]] ERROR c.n.p.n.s.impl.NowPaymentServiceImpl - [NowPayment] Error: 400
2025-08-26 15:01:09.893 [[http-nio-8080-exec-4]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [https://api-sandbox.nowpayments.io/v1/full-currencies]
2025-08-26 15:01:09.893 [[http-nio-8080-exec-4]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 15:01:09.893 [[http-nio-8080-exec-4]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [x-api-key: H3VKFJT-8VSM715-QRWG6JH-1MMACJW]
2025-08-26 15:01:10.179 [[http-nio-8080-exec-4]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [403]
2025-08-26 15:01:10.179 [[http-nio-8080-exec-4]] INFO  c.n.pro.utils.httpClient.ApiClient - Error message: [{"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}]
2025-08-26 15:01:10.179 [[http-nio-8080-exec-4]] INFO  c.n.pro.utils.httpClient.ApiClient - Status NOT OK. throw Error code: [400]
2025-08-26 15:01:10.179 [[http-nio-8080-exec-4]] ERROR c.n.pro.utils.httpClient.ApiClient -  CALL PARTNER [Error] https://api-sandbox.nowpayments.io/v1/full-currencies
java.lang.Exception: 400 Error when map code: 403
	at com.neoproxy.pro.utils.httpClient.ApiClient.invoke(ApiClient.java:91)
	at com.neoproxy.pro.utils.httpClient.ApiClient.callAPI(ApiClient.java:61)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getFullCurrencies(NowPaymentServiceImpl.java:73)
	at com.neoproxy.pro.controller.client.PaymentController.getFullCurrencies(PaymentController.java:46)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.neoproxy.pro.config.JwtRequestFilter.doFilterInternal(JwtRequestFilter.java:33)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1732)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-26 15:01:10.179 [[http-nio-8080-exec-4]] ERROR c.n.p.n.s.impl.NowPaymentServiceImpl - [NowPayment] Error: 400
2025-08-26 15:01:35.750 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:01:35.750 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:02:05.750 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:02:05.751 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:02:35.751 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:02:35.751 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:03:05.751 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:03:05.751 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:03:35.752 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:03:35.752 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:04:05.752 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:04:05.752 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:04:35.753 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:04:35.753 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:05:05.754 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:05:05.754 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:05:32.563 [[http-nio-8080-exec-4]] INFO  c.n.p.s.impl.LicenseServiceImpl - 2025-08-26 - 2025-08-27T00:00
2025-08-26 15:05:32.563 [[http-nio-8080-exec-9]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [https://api-sandbox.nowpayments.io/v1/merchant/coins]
2025-08-26 15:05:32.563 [[http-nio-8080-exec-9]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 15:05:32.563 [[http-nio-8080-exec-9]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [x-api-key: H3VKFJT-8VSM715-QRWG6JH-1MMACJW]
2025-08-26 15:05:33.310 [[http-nio-8080-exec-9]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [403]
2025-08-26 15:05:33.310 [[http-nio-8080-exec-9]] INFO  c.n.pro.utils.httpClient.ApiClient - Error message: [{"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}]
2025-08-26 15:05:33.310 [[http-nio-8080-exec-9]] INFO  c.n.pro.utils.httpClient.ApiClient - Status NOT OK. throw Error code: [400]
2025-08-26 15:05:33.310 [[http-nio-8080-exec-9]] ERROR c.n.pro.utils.httpClient.ApiClient -  CALL PARTNER [Error] https://api-sandbox.nowpayments.io/v1/merchant/coins
java.lang.Exception: 400 Error when map code: 403
	at com.neoproxy.pro.utils.httpClient.ApiClient.invoke(ApiClient.java:91)
	at com.neoproxy.pro.utils.httpClient.ApiClient.callAPI(ApiClient.java:61)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getSelectedCurrencies(NowPaymentServiceImpl.java:58)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getFullCurrencies(NowPaymentServiceImpl.java:72)
	at com.neoproxy.pro.controller.client.PaymentController.getFullCurrencies(PaymentController.java:46)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.neoproxy.pro.config.JwtRequestFilter.doFilterInternal(JwtRequestFilter.java:33)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1732)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-26 15:05:33.310 [[http-nio-8080-exec-9]] ERROR c.n.p.n.s.impl.NowPaymentServiceImpl - [NowPayment] Error: 400
2025-08-26 15:05:33.574 [[http-nio-8080-exec-9]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [https://api-sandbox.nowpayments.io/v1/full-currencies]
2025-08-26 15:05:33.574 [[http-nio-8080-exec-9]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 15:05:33.574 [[http-nio-8080-exec-9]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [x-api-key: H3VKFJT-8VSM715-QRWG6JH-1MMACJW]
2025-08-26 15:05:33.811 [[http-nio-8080-exec-9]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [403]
2025-08-26 15:05:33.811 [[http-nio-8080-exec-9]] INFO  c.n.pro.utils.httpClient.ApiClient - Error message: [{"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}]
2025-08-26 15:05:33.811 [[http-nio-8080-exec-9]] INFO  c.n.pro.utils.httpClient.ApiClient - Status NOT OK. throw Error code: [400]
2025-08-26 15:05:33.811 [[http-nio-8080-exec-9]] ERROR c.n.pro.utils.httpClient.ApiClient -  CALL PARTNER [Error] https://api-sandbox.nowpayments.io/v1/full-currencies
java.lang.Exception: 400 Error when map code: 403
	at com.neoproxy.pro.utils.httpClient.ApiClient.invoke(ApiClient.java:91)
	at com.neoproxy.pro.utils.httpClient.ApiClient.callAPI(ApiClient.java:61)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getFullCurrencies(NowPaymentServiceImpl.java:73)
	at com.neoproxy.pro.controller.client.PaymentController.getFullCurrencies(PaymentController.java:46)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.neoproxy.pro.config.JwtRequestFilter.doFilterInternal(JwtRequestFilter.java:33)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1732)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-26 15:05:33.812 [[http-nio-8080-exec-9]] ERROR c.n.p.n.s.impl.NowPaymentServiceImpl - [NowPayment] Error: 400
2025-08-26 15:05:35.754 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:05:35.754 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:06:05.754 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:06:05.754 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:06:35.754 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:06:35.755 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:07:05.755 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:07:05.755 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:07:35.755 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:07:35.756 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:08:05.756 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:08:05.756 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:08:35.756 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:08:35.757 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:09:05.757 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:09:05.757 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:09:35.758 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:09:35.758 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:09:46.367 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@6d07055d: (connection has passed maxLifetime)
2025-08-26 15:09:49.602 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@660d1b28
2025-08-26 15:09:56.729 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@7c619b0f: (connection has passed maxLifetime)
2025-08-26 15:09:57.752 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Add connection elided, waiting 0, queue 1
2025-08-26 15:09:57.752 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@2e65a290: (connection has passed maxLifetime)
2025-08-26 15:09:59.739 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@5ac7667
2025-08-26 15:10:01.327 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Add connection elided, waiting 0, queue 1
2025-08-26 15:10:01.327 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@7b519eb9: (connection has passed maxLifetime)
2025-08-26 15:10:02.154 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Add connection elided, waiting 0, queue 2
2025-08-26 15:10:02.154 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@50ca141a: (connection has passed maxLifetime)
2025-08-26 15:10:02.637 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@b6b88a2
2025-08-26 15:10:02.637 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=8, active=0, idle=8, waiting=0)
2025-08-26 15:10:04.872 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@1322b575: (connection has passed maxLifetime)
2025-08-26 15:10:04.872 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Add connection elided, waiting 0, queue 2
2025-08-26 15:10:05.492 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@f541d17
2025-08-26 15:10:05.492 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=8, active=0, idle=8, waiting=0)
2025-08-26 15:10:05.759 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=8, active=0, idle=8, waiting=0)
2025-08-26 15:10:05.759 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:10:07.855 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@1a54f4f8: (connection has passed maxLifetime)
2025-08-26 15:10:07.855 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Add connection elided, waiting 0, queue 2
2025-08-26 15:10:08.598 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2a797dd6
2025-08-26 15:10:08.599 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=8, active=0, idle=8, waiting=0)
2025-08-26 15:10:08.843 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Add connection elided, waiting 0, queue 2
2025-08-26 15:10:08.843 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@31c1e9b7: (connection has passed maxLifetime)
2025-08-26 15:10:11.680 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@4e57e473
2025-08-26 15:10:11.680 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=8, active=0, idle=8, waiting=0)
2025-08-26 15:10:13.248 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@67655119: (connection has passed maxLifetime)
2025-08-26 15:10:13.248 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Add connection elided, waiting 0, queue 2
2025-08-26 15:10:15.402 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@401d593e
2025-08-26 15:10:15.402 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=8, active=0, idle=8, waiting=0)
2025-08-26 15:10:18.304 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@49f9fc8
2025-08-26 15:10:18.304 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=9, active=0, idle=9, waiting=0)
2025-08-26 15:10:20.880 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Add connection elided, waiting 0, queue 1
2025-08-26 15:10:20.880 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@42a6c28d: (connection has passed maxLifetime)
2025-08-26 15:10:21.336 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@13a77396
2025-08-26 15:10:21.336 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=9, active=0, idle=9, waiting=0)
2025-08-26 15:10:24.380 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@62a269ed
2025-08-26 15:10:24.381 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:10:35.759 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:10:35.760 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:11:03.976 [[http-nio-8080-exec-9]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [https://api-sandbox.nowpayments.io/v1/merchant/coins]
2025-08-26 15:11:03.976 [[http-nio-8080-exec-9]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 15:11:03.976 [[http-nio-8080-exec-9]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [x-api-key: H3VKFJT-8VSM715-QRWG6JH-1MMACJW]
2025-08-26 15:11:03.976 [[http-nio-8080-exec-10]] INFO  c.n.p.s.impl.LicenseServiceImpl - 2025-08-26 - 2025-08-27T00:00
2025-08-26 15:11:05.086 [[http-nio-8080-exec-9]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [403]
2025-08-26 15:11:05.086 [[http-nio-8080-exec-9]] INFO  c.n.pro.utils.httpClient.ApiClient - Error message: [{"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}]
2025-08-26 15:11:05.086 [[http-nio-8080-exec-9]] INFO  c.n.pro.utils.httpClient.ApiClient - Status NOT OK. throw Error code: [400]
2025-08-26 15:11:05.087 [[http-nio-8080-exec-9]] ERROR c.n.pro.utils.httpClient.ApiClient -  CALL PARTNER [Error] https://api-sandbox.nowpayments.io/v1/merchant/coins
java.lang.Exception: 400 Error when map code: 403
	at com.neoproxy.pro.utils.httpClient.ApiClient.invoke(ApiClient.java:91)
	at com.neoproxy.pro.utils.httpClient.ApiClient.callAPI(ApiClient.java:61)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getSelectedCurrencies(NowPaymentServiceImpl.java:58)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getFullCurrencies(NowPaymentServiceImpl.java:72)
	at com.neoproxy.pro.controller.client.PaymentController.getFullCurrencies(PaymentController.java:46)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.neoproxy.pro.config.JwtRequestFilter.doFilterInternal(JwtRequestFilter.java:33)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1732)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-26 15:11:05.087 [[http-nio-8080-exec-9]] ERROR c.n.p.n.s.impl.NowPaymentServiceImpl - [NowPayment] Error: 400
2025-08-26 15:11:05.359 [[http-nio-8080-exec-9]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [https://api-sandbox.nowpayments.io/v1/full-currencies]
2025-08-26 15:11:05.359 [[http-nio-8080-exec-9]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-26 15:11:05.359 [[http-nio-8080-exec-9]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [x-api-key: H3VKFJT-8VSM715-QRWG6JH-1MMACJW]
2025-08-26 15:11:05.596 [[http-nio-8080-exec-9]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [403]
2025-08-26 15:11:05.596 [[http-nio-8080-exec-9]] INFO  c.n.pro.utils.httpClient.ApiClient - Error message: [{"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}]
2025-08-26 15:11:05.596 [[http-nio-8080-exec-9]] INFO  c.n.pro.utils.httpClient.ApiClient - Status NOT OK. throw Error code: [400]
2025-08-26 15:11:05.596 [[http-nio-8080-exec-9]] ERROR c.n.pro.utils.httpClient.ApiClient -  CALL PARTNER [Error] https://api-sandbox.nowpayments.io/v1/full-currencies
java.lang.Exception: 400 Error when map code: 403
	at com.neoproxy.pro.utils.httpClient.ApiClient.invoke(ApiClient.java:91)
	at com.neoproxy.pro.utils.httpClient.ApiClient.callAPI(ApiClient.java:61)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getFullCurrencies(NowPaymentServiceImpl.java:73)
	at com.neoproxy.pro.controller.client.PaymentController.getFullCurrencies(PaymentController.java:46)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.neoproxy.pro.config.JwtRequestFilter.doFilterInternal(JwtRequestFilter.java:33)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1732)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-26 15:11:05.596 [[http-nio-8080-exec-9]] ERROR c.n.p.n.s.impl.NowPaymentServiceImpl - [NowPayment] Error: 400
2025-08-26 15:11:05.760 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:11:05.760 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:11:35.760 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:11:35.761 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:12:05.761 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:12:05.761 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:12:35.762 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:12:35.762 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:13:05.762 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:13:05.762 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:13:35.771 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:13:35.771 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:14:05.772 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:14:05.772 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:14:35.773 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:14:35.773 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:15:05.774 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:15:05.774 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:15:35.774 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:15:35.774 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:16:05.775 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:16:05.775 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:16:35.775 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:16:35.776 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:17:05.776 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:17:05.776 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:17:35.776 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:17:35.777 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:18:05.777 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:18:05.777 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-26 15:18:35.777 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-26 15:18:35.778 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
