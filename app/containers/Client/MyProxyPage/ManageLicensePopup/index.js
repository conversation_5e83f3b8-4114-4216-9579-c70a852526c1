import React from 'react';
import {injectIntl} from 'react-intl';
import Button from 'components/common/Button';
import ButtonLink from 'components/ButtonLink';
import ActionDialog from 'components/common/ActionDialog';
import {TO, convertDropdownList} from 'utils/utilHelper';
import messages from '../messages';
import styled from 'styled-components';
import {Formik, Form} from 'formik';
import {Row, Col} from 'reactstrap';
import get from 'lodash/get';
import DropdownList from 'components/common/DropdownList';
import {compose} from 'redux';
import WithHandleAlert from 'containers/WithHandleAlert';
import {errorCode} from 'constants/responseCode';
import * as Yup from 'yup';
import CommonTabs from 'components/common/Tabs';
import {Tab} from '@blueprintjs/core';
import Toggle from 'components/common/Toggle';
import {extendLicense, toggleAutoRenewal, cancelVpn, purchaseVpn, expressExtendLicense} from 'services/user.service';

const StyledContainer = styled(ActionDialog)`
  .bp3-dialog {
    width: 400px;
  }

  .bp3-tab-panel {
    margin-top: 5px !important;
    padding: 5px !important;
  }

  .bp3-tab[aria-selected="true"] {
    border-radius: 0;
    box-shadow: none !important;
  }
`;

const TabContent = styled.div`
  padding: 10px 0;

  .tab-description {
    color: ${(props) => props.theme.colors.gray600};
    font-size: 14px;
    margin-bottom: 20px;
    line-height: 1.5;
  }

  .express-extension-highlight {
    background-color: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: flex-start;

    .info-icon {
      color: #4a90e2;
      font-size: 18px;
      margin-right: 12px;
      margin-top: 2px;
      flex-shrink: 0;
    }

    .content {
      flex: 1;

      .title {
        font-weight: 600;
        color: #2c5aa0;
        font-size: 16px;
        margin-bottom: 8px;
      }

      .description {
        color: #4a90e2;
        font-size: 14px;
        line-height: 1.4;
        margin: 0;
      }
    }
  }

  .auto-renewal-section {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .auto-renewal-content {
      flex: 1;

      .auto-renewal-title {
        font-weight: 600;
        color: #495057;
        font-size: 16px;
        margin-bottom: 4px;
      }

      .auto-renewal-description {
        color: #6c757d;
        font-size: 14px;
        margin: 0;
      }
    }

    .auto-renewal-toggle {
      flex-shrink: 0;
      margin-left: 16px;
    }
  }

  .vpn-addon-card {
    background-color: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 12px;
    padding: 10px;
    text-align: center;
    margin-bottom: 10px;

    .vpn-header {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 12px;

      .vpn-icon {
        width: 24px;
        height: 24px;
        border: 2px solid #4a90e2;
        border-radius: 50%;
        margin-right: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: transparent;

        &::before {
          content: '';
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #4a90e2;
        }
      }

      .vpn-title {
        font-size: 18px;
        font-weight: 600;
        color: #2c5aa0;
        margin: 0;
      }
    }

    .vpn-price {
      font-size: 32px;
      font-weight: 700;
      color: #4a90e2;
      margin-bottom: 8px;

      .currency {
        font-size: 24px;
      }

      .period {
        font-size: 16px;
        font-weight: 400;
        color: #6c757d;
      }
    }

    .vpn-description {
      color: #6c757d;
      font-size: 14px;
      margin-bottom: 20px;
      line-height: 1.4;
    }

    .vpn-features {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
      gap: 15px;

      .feature-column {
        flex: 1;
        text-align: left;
        border: 1px solid #b3d9ff;
        border-radius: 12px;
        background-color: rgb(255 255 255);
        padding: 25px;
        margin: 5px;

        .feature-header {
          display: flex;
          align-items: center;
          margin-bottom: 12px;

          .feature-icon {
            width: 20px;
            height: 20px;
            border: 2px solid #4a90e2;
            border-radius: 50%;
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: transparent;

            &::before {
              content: '';
              width: 6px;
              height: 6px;
              border-radius: 50%;
              background-color: #4a90e2;
            }
          }

          .feature-name {
            font-weight: 600;
            color: #2c5aa0;
            font-size: 14px;
          }
        }

        .feature-list {
          list-style: none;
          padding: 0;
          margin: 0;

          .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
            font-size: 13px;
            color: #6c757d;

            &::before {
              content: '✓';
              color: #28a745;
              font-weight: bold;
              margin-right: 8px;
              font-size: 12px;
            }
          }
        }
      }
    }

    .vpn-purchase-btn {
      background-color: #4a90e2;
      border: none;
      border-radius: 8px;
      color: white;
      padding: 12px 24px;
      font-size: 14px;
      font-weight: 600;
      width: 100%;
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background-color: #357abd;
      }

      &:disabled {
        background-color: #6c757d;
        cursor: not-allowed;
      }
    }

    ul li {
      list-style-type: none;
      padding: 0;
      margin: 0;
    }
  }

  // Cancel VPN Tab Styles

  .cancel-vpn-container {
    .vpn-active-card {
      background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
      border: 1px solid #d4edda;
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 20px;
      position: relative;

      .vpn-active-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;

        .vpn-active-left {
          display: flex;
          align-items: center;

          .vpn-shield-icon {
            width: 32px;
            height: 32px;
            background-color: #28a745;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            position: relative;

            &::before {
              content: '';
              width: 16px;
              height: 16px;
              background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='white' viewBox='0 0 24 24'%3E%3Cpath d='M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z'/%3E%3C/svg%3E");
              background-size: contain;
              background-repeat: no-repeat;
            }
          }

          .vpn-active-title {
            font-size: 18px;
            font-weight: 600;
            color: #155724;
            margin: 0;
          }
        }

        .vpn-active-status {
          background-color: #28a745;
          color: white;
          padding: 6px 16px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 600;
          text-transform: uppercase;
        }
      }

      .vpn-active-subtitle {
        color: #6c757d;
        font-size: 14px;
        margin-bottom: 8px;
        margin-left: 44px;
      }

      .vpn-active-price {
        font-size: 18px;
        font-weight: 600;
        color: #155724;
        margin-left: 44px;
      }
    }

    .current-benefits-section {
      background-color: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 12px;
      padding: 24px;
      margin-bottom: 20px;

      .benefits-title {
        font-size: 18px;
        font-weight: 600;
        color: #495057;
        margin-bottom: 20px;
      }

      .benefits-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;

        .benefit-item {
          display: flex;
          align-items: center;
          font-size: 14px;
          color: #495057;

          &::before {
            content: '';
            width: 16px;
            height: 16px;
            background-color: #28a745;
            border-radius: 50%;
            margin-right: 12px;
            position: relative;
            flex-shrink: 0;
          }

          &::after {
            content: '✓';
            position: absolute;
            color: white;
            font-weight: bold;
            font-size: 10px;
            margin-left: -11px;
            margin-top: -1px;
          }
        }
      }
    }

    .cancel-warning-section {
      background-color: #fff5f5;
      border: 1px solid #fed7d7;
      border-radius: 12px;
      padding: 24px;
      margin-bottom: 24px;

      .warning-header {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        .warning-icon {
          width: 24px;
          height: 24px;
          background-color: #e53e3e;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          position: relative;

          &::before {
            content: '';
            width: 12px;
            height: 12px;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='white' viewBox='0 0 24 24'%3E%3Cpath d='M12,2L13.09,8.26L22,9L13.09,9.74L12,16L10.91,9.74L2,9L10.91,8.26L12,2M12,7A2,2 0 0,0 10,9A2,2 0 0,0 12,11A2,2 0 0,0 14,9A2,2 0 0,0 12,7Z'/%3E%3C/svg%3E");
            background-size: contain;
            background-repeat: no-repeat;
          }
        }

        .warning-title {
          font-size: 18px;
          font-weight: 600;
          color: #c53030;
          margin: 0;
        }
      }

      .warning-description {
        color: #742a2a;
        font-size: 14px;
        line-height: 1.5;
        margin-left: 36px;
      }
    }

    .cancel-vpn-btn {
      background-color: #e53e3e;
      border: none;
      border-radius: 8px;
      color: white;
      padding: 14px 24px;
      font-size: 14px;
      font-weight: 600;
      width: 100%;
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background-color: #c53030;
      }

      &:disabled {
        background-color: #6c757d;
        cursor: not-allowed;
      }
    }
  }
`;

class ManageLicensePopup extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      activeTab: 'express-extend',
      initData: {
        packageId: '',
        autoRenewal: '0',
        vpnType: 'OPEN_VPN', // Thêm vpnType vào initData
      },
      hasVpnAddon: false,
    };
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.selectedIds !== undefined && nextProps.dataList) {
      // Get selected license data
      const selectedLicense = nextProps.dataList.find(license =>
        nextProps.selectedIds.includes(license.uuid)
      );

      // Get autoRenewal value from selected license data
      const autoRenewalValue = selectedLicense && selectedLicense.autoRenewal ? '1' : '0';

      // Get VPN addon status from vpnType field
      // Assuming vpnType exists and is not null/empty when VPN addon is active
      const hasVpnAddon = selectedLicense && selectedLicense.vpnType && selectedLicense.vpnType !== '';

      this.setState({
        initData: {
          packageId: '',
          autoRenewal: autoRenewalValue,
          vpnType: 'OPEN_VPN', // Mặc định là OpenVPN
        },
        hasVpnAddon: hasVpnAddon,
      });
    }
  }

  handleTabChange = (newTabId) => {
    this.setState({activeTab: newTabId});
  };

  handleToggleAutoRenewal = async (newValue, {setSubmitting}) => {
    const {forceRefresh, intl, selectedIds} = this.props;

    const dataSubmit = {
      uuids: selectedIds,
      autoRenewal: newValue === '1',
    };

    const [err, response] = await TO(toggleAutoRenewal(dataSubmit));

    if (err) {
      this.props.handleAlertError(intl.formatMessage(messages.msgUpdateFailed));
    } else if (response && response.code === errorCode.SUCCESS) {
      const message = newValue === '1' ? 'Auto renewal enabled successfully!' : 'Auto renewal disabled successfully!';
      this.props.handleAlertSuccess(message);
      forceRefresh();
    } else if (response && response.message) {
      this.props.handleAlertError(response.message);
    } else {
      this.props.handleAlertError(intl.formatMessage(messages.msgUpdateFailed));
    }

    if (setSubmitting) {
      setSubmitting(false);
    }
  };

  handleExpressExtend = async (values, {setSubmitting}) => {
    const {forceRefresh, handleOnClose, intl, selectedIds} = this.props;

    // First handle auto renewal toggle if changed
    const selectedLicense = this.props.dataList.find(license =>
      selectedIds.includes(license.uuid)
    );
    const currentAutoRenewal = selectedLicense && selectedLicense.autoRenewal ? '1' : '0';

    if (values.autoRenewal !== currentAutoRenewal) {
      await this.handleToggleAutoRenewal(values.autoRenewal, {setSubmitting: false});
    }

    // Call Express Extend API endpoint
    const [err, response] = await TO(expressExtendLicense({uuids: selectedIds}));

    if (err) {
      this.props.handleAlertError(intl.formatMessage(messages.msgUpdateFailed));
    }
    if (response && response.code === errorCode.SUCCESS) {
      this.props.handleAlertSuccess('Express extend completed successfully!');
      handleOnClose();
      forceRefresh();
    } else if (response && response.message) {
      this.props.handleAlertError(response.message);
    } else {
      this.props.handleAlertError(intl.formatMessage(messages.msgUpdateFailed));
    }
    setSubmitting(false);
  };

  handleUpgradePackage = async (values, {setSubmitting}) => {
    const {forceRefresh, handleOnClose, intl, selectedIds} = this.props;

    // First handle auto renewal toggle if changed
    const selectedLicense = this.props.dataList.find(license =>
      selectedIds.includes(license.uuid)
    );
    const currentAutoRenewal = selectedLicense && selectedLicense.autoRenewal ? '1' : '0';

    if (values.autoRenewal !== currentAutoRenewal) {
      await this.handleToggleAutoRenewal(values.autoRenewal, {setSubmitting: false});
    }

    const dataSubmit = {
      packageId: values.packageId,
      uuids: selectedIds,
      autoRenewal: values.autoRenewal === '1',
    };

    const [err, response] = await TO(extendLicense(dataSubmit));
    if (err) {
      this.props.handleAlertError(intl.formatMessage(messages.msgUpdateFailed));
    }
    if (response.code === errorCode.SUCCESS) {
      this.props.handleAlertSuccess('Package upgraded successfully!');
      handleOnClose();
      forceRefresh();
    } else if (response.message) {
      this.props.handleAlertError(response.message);
    } else {
      this.props.handleAlertError(intl.formatMessage(messages.msgUpdateFailed));
    }
    setSubmitting(false);
  };

  handleVpnAction = async (action, {setSubmitting}) => {
    const {forceRefresh, handleOnClose, intl, selectedIds} = this.props;

    if (action === 'purchase') {
      const dataSubmit = {
        licenseUuids: selectedIds,
        vpnType: this.state.initData.vpnType // Sử dụng vpnType từ state
      };

      const [err, response] = await TO(purchaseVpn(dataSubmit));

      if (err) {
        this.props.handleAlertError(intl.formatMessage(messages.msgUpdateFailed));
      } else if (response && response.code === errorCode.SUCCESS) {
        const transactionData = response.data;
        const message = `VPN Add-on purchased successfully! Transaction: ${transactionData.uuid}, Amount: $${transactionData.amount} ${transactionData.currency}`;
        this.props.handleAlertSuccess(message);
        handleOnClose();
        forceRefresh();
      } else if (response && response.message) {
        this.props.handleAlertError(response.message);
      } else {
        this.props.handleAlertError(intl.formatMessage(messages.msgUpdateFailed));
      }
    } else {
      // Handle other actions (cancel, etc.)
      const message = 'VPN Add-on action completed successfully!';
      this.props.handleAlertSuccess(message);
      handleOnClose();
      forceRefresh();
    }

    setSubmitting(false);
  };

  getValidationSchema = () => {
    const {activeTab} = this.state;

    if (activeTab === 'upgrade-package') {
      return Yup.object().shape({
        packageId: Yup.string().required('Package is required'),
      });
    }

    return Yup.object().shape({});
  };

  renderExpressExtendTab = (formProps) => {
    const {packageUnit} = this.props;

    return (
      <TabContent>
        <div className="express-extension-highlight">
          <i className="fa fa-info-circle info-icon"></i>
          <div className="content">
            <div className="title">Express Extension</div>
            <div className="description">
              Instantly extend your license with the same package plan ({packageUnit || 'Trial T-Mobile'}). No
              additional configuration needed.
            </div>
          </div>
        </div>

        <div className="auto-renewal-section">
          <div className="auto-renewal-content">
            <div className="auto-renewal-title">Auto Renewal</div>
            <div className="auto-renewal-description">
              Automatically renew when license expires
            </div>
          </div>
          <div className="auto-renewal-toggle">
            <Toggle
              name="autoRenewal"
              value={formProps.values.autoRenewal}
              setFieldValue={(field, value) => {
                formProps.setFieldValue(field, value);
                // Call API immediately when toggle changes
                this.handleToggleAutoRenewal(value, {setSubmitting: formProps.setSubmitting});
              }}
            />
          </div>
        </div>

        <div className="d-flex flex-column align-items-center mt-4">
          <div className="d-flex">
            <Button
              primary
              type="submit"
              className="min-width-100 mt-4 mr-1"
              loading={formProps.isSubmitting}
              onClick={() => this.handleExpressExtend(formProps.values, {setSubmitting: formProps.setSubmitting})}
            >
              Express Extend
            </Button>
          </div>
        </div>
      </TabContent>
    );
  };

  renderUpgradePackageTab = (formProps) => {
    const {packages, packageUnit, location} = this.props;
    console.log('---renderUpgradePackageTab-packageUnit--: ' + packageUnit);
    console.log('---renderUpgradePackageTab-location--: ' + location);

    const upgrade = {
      MINUTES: ['MINUTES', 'DAY', 'WEEK', 'MONTH'],
      DAY: ['DAY', 'WEEK', 'MONTH'],
      WEEK: ['WEEK', 'MONTH'],
      MONTH: ['MONTH'],
    };

    let packagesOptions = [];
    const packageUpgrades = upgrade[packageUnit];
    if (packageUpgrades) {
      const filterPackages = packages.filter((i) =>
        packageUpgrades.includes(i.packageUnit) &&
        i.location === location
      );
      packagesOptions = convertDropdownList(filterPackages.map((i) => {
        let packageName = `${i.name}`;
        if (i.promotionDes) {
          packageName += ' ' + i.promotionDes;
        }
        return {
          name: packageName,
          id: i.uuid,
        };
      }));
    }

    return (
      <TabContent>
        <div className="tab-description">
          Switch to a different package plan while extending your license. Get better value with longer-term plans.
        </div>

        <Row>
          <Col md={{size: 12}}>
            <DropdownList
              label="Packages"
              isAsterisk
              name="packageId"
              value={packagesOptions.find((option) =>
                option.value === get(formProps.values, 'packageId')
              )}
              options={packagesOptions}
              onChange={(option) => {
                formProps.setFieldValue('packageId', option.value);
              }}
              {...formProps}
            />
          </Col>
        </Row>

        {/*<div className="auto-renewal-section">*/}
        {/*  <div className="auto-renewal-content">*/}
        {/*    <div className="auto-renewal-title">Auto Renewal</div>*/}
        {/*    <div className="auto-renewal-description">*/}
        {/*      Automatically renew when license expires*/}
        {/*    </div>*/}
        {/*  </div>*/}
        {/*  <div className="auto-renewal-toggle">*/}
        {/*    <Toggle*/}
        {/*      name="autoRenewal"*/}
        {/*      value={formProps.values.autoRenewal}*/}
        {/*      setFieldValue={(field, value) => {*/}
        {/*        formProps.setFieldValue(field, value);*/}
        {/*        // Call API immediately when toggle changes*/}
        {/*        this.handleToggleAutoRenewal(value, { setSubmitting: formProps.setSubmitting });*/}
        {/*      }}*/}
        {/*    />*/}
        {/*  </div>*/}
        {/*</div>*/}

        <div className="d-flex flex-column align-items-center mt-4">
          <div className="d-flex">
            <Button
              primary
              type="submit"
              className="min-width-100 mt-4 mr-1"
              loading={formProps.isSubmitting}
              onClick={() => this.handleUpgradePackage(formProps.values, {setSubmitting: formProps.setSubmitting})}
            >
              Upgrade Package
            </Button>
          </div>
        </div>
      </TabContent>
    );
  };

  renderVpnAddonsTab = (formProps) => {
    const {vpnFee = 0} = this.props;

    // Tạo options cho VPN type
    const vpnTypeOptions = [
      { value: 'OPEN_VPN', label: 'OpenVPN' },
      { value: 'WIRE_GUARD', label: 'WireGuard' }
    ];

    return (
      <TabContent>
        <div className="vpn-addon-card">
          <div className="vpn-header">
            <div className="vpn-icon"></div>
            <h3 className="vpn-title">Premium VPN Add-on</h3>
          </div>
          <div className="vpn-price">
            <span className="currency">$</span>{vpnFee}<span className="period">/month</span>
          </div>
          <div className="vpn-description">
            Choose your preferred VPN protocol and get premium access
          </div>

          {/* Thay thế 2 column bằng dropdown selection */}
          <div className="vpn-type-selection" style={{ marginBottom: '20px' }}>
            <label style={{
              display: 'block',
              marginBottom: '8px',
              fontWeight: '600',
              color: '#2c5aa0'
            }}>
              Select VPN Protocol:
            </label>
            <DropdownList
              name="vpnType"
              value={vpnTypeOptions.find(option => option.value === formProps.values.vpnType)}
              options={vpnTypeOptions}
              onChange={(option) => {
                formProps.setFieldValue('vpnType', option.value);
              }}
              placeholder="Choose VPN Protocol"
            />
          </div>

          {/* Hiển thị thông tin về protocol được chọn */}
          <div className="vpn-protocol-info" style={{
            backgroundColor: '#f8f9fa',
            border: '1px solid #e9ecef',
            borderRadius: '8px',
            padding: '16px',
            marginBottom: '20px'
          }}>
            {formProps.values.vpnType === 'WIRE_GUARD' ? (
              <div>
                <h4 style={{ color: '#2c5aa0', marginBottom: '8px' }}>WireGuard Features:</h4>
                <ul style={{ margin: 0, paddingLeft: '20px', color: '#6c757d' }}>
                  <li>Faster speeds</li>
                  <li>Lower latency</li>
                  <li>Modern encryption</li>
                </ul>
              </div>
            ) : (
              <div>
                <h4 style={{ color: '#2c5aa0', marginBottom: '8px' }}>OpenVPN Features:</h4>
                <ul style={{ margin: 0, paddingLeft: '20px', color: '#6c757d' }}>
                  <li>High compatibility</li>
                  <li>Stable connections</li>
                  <li>Advanced encryption</li>
                </ul>
              </div>
            )}
          </div>

          <button
            className="vpn-purchase-btn"
            onClick={() => this.handleVpnAction('purchase', {setSubmitting: formProps.setSubmitting})}
            disabled={formProps.isSubmitting}
          >
            Purchase {formProps.values.vpnType === 'WIRE_GUARD' ? 'WireGuard' : 'OpenVPN'} VPN - ${vpnFee}/month
          </button>
        </div>
      </TabContent>
    );
  };

  handleCancelVpn = async (values, {setSubmitting}) => {
    const {forceRefresh, handleOnClose, intl, selectedIds} = this.props;

    const dataSubmit = {
      licenseUuids: selectedIds,
    };

    const [err, response] = await TO(cancelVpn(dataSubmit));

    if (err) {
      this.props.handleAlertError(intl.formatMessage(messages.msgUpdateFailed));
    } else if (response && response.code === errorCode.SUCCESS) {
      this.props.handleAlertSuccess('VPN Add-on cancelled successfully!');
      handleOnClose();
      forceRefresh();
    } else if (response && response.message) {
      this.props.handleAlertError(response.message);
    } else {
      this.props.handleAlertError(intl.formatMessage(messages.msgUpdateFailed));
    }

    setSubmitting(false);
  };

  renderCancelVpnTab = (formProps) => {
    const {vpnFee = 0} = this.props;

    return (
      <TabContent>
        <div className="cancel-vpn-container">
          <div className="vpn-active-card">
            <div className="vpn-active-header">
              <div className="vpn-active-left">
                <div className="vpn-shield-icon"></div>
                <h3 className="vpn-active-title">VPN Add-on Active</h3>
              </div>
              <span className="vpn-active-status">Active</span>
            </div>
            <div className="vpn-active-subtitle">Premium VPN with protocol flexibility</div>
            <div className="vpn-active-price">${vpnFee}/month</div>
          </div>

          {/*<div className="current-benefits-section">*/}
          {/*  <div className="benefits-title">Current Benefits</div>*/}
          {/*  <div className="benefits-grid">*/}
          {/*    <div className="benefit-item">WireGuard & OpenVPN access</div>*/}
          {/*    <div className="benefit-item">Enhanced security</div>*/}
          {/*    <div className="benefit-item">Protocol switching anytime</div>*/}
          {/*    <div className="benefit-item">Priority support</div>*/}
          {/*  </div>*/}
          {/*</div>*/}

          <div className="cancel-warning-section">
            <div className="warning-header">
              <div className="warning-icon"></div>
              <h4 className="warning-title">Cancel VPN Add-on</h4>
            </div>
            <div className="warning-description">
              Canceling will immediately remove VPN add-on features. You'll revert to basic VPN.
            </div>
          </div>

          <button
            className="cancel-vpn-btn"
            onClick={() => this.handleCancelVpn(formProps.values, {setSubmitting: formProps.setSubmitting})}
            disabled={formProps.isSubmitting}
          >
            Cancel VPN Add-on
          </button>
        </div>
      </TabContent>
    );
  };

  render() {
    const {intl, isOpen, handleOnClose} = this.props;
    const {activeTab, initData, hasVpnAddon} = this.state;

    return (
      <StyledContainer
        portalClassName="custom-portal manage-license-popup"
        title="Manage License"
        usePortal
        canOutsideClickClose
        canEscapeKeyClose
        isOpen={isOpen}
        onClose={handleOnClose}
        width={600}
      >
        <div className="m-4">
          <Formik
            initialValues={initData}
            enableReinitialize
            validationSchema={this.getValidationSchema()}
            render={(formProps) => (
              <Form>
                <CommonTabs selectedTabId={activeTab} onChange={this.handleTabChange}>
                  <Tab
                    id="express-extend"
                    title="Express Extend"
                    panel={this.renderExpressExtendTab(formProps)}
                  />
                  <Tab
                    id="upgrade-package"
                    title="Upgrade Package"
                    panel={this.renderUpgradePackageTab(formProps)}
                  />
                  {!hasVpnAddon && (
                    <Tab
                      id="vpn-addons"
                      title="VPN Add-ons"
                      panel={this.renderVpnAddonsTab(formProps)}
                    />
                  )}
                  {hasVpnAddon && (
                    <Tab
                      id="cancel-vpn"
                      title="Cancel VPN"
                      panel={this.renderCancelVpnTab(formProps)}
                    />
                  )}
                </CommonTabs>

                <div className="d-flex justify-content-center mt-1">
                  <ButtonLink
                    onClick={handleOnClose}
                    type="button"
                  >
                    {intl.formatMessage(messages.close)}
                  </ButtonLink>
                </div>
              </Form>
            )}
          />
        </div>
      </StyledContainer>
    );
  }
}

ManageLicensePopup.propTypes = {};

export default compose(
  WithHandleAlert,
  injectIntl
)(ManageLicensePopup);
