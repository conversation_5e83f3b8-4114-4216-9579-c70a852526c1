import React from 'react';
import { injectIntl } from 'react-intl';
import Button from 'components/common/Button';
import ButtonLink from 'components/ButtonLink';
import ActionDialog from 'components/common/ActionDialog';
import { TO, convertDropdownList } from 'utils/utilHelper';
import messages from '../messages';
import styled from 'styled-components';
import { Formik, Form } from 'formik';
import { Row, Col } from 'reactstrap';
import get from 'lodash/get';
import DropdownList from 'components/common/DropdownList';
import { compose } from 'redux';
import WithHandleAlert from 'containers/WithHandleAlert';
import { changeLocation } from 'services/user.service';
import { errorCode } from 'constants/responseCode';
import * as Yup from 'yup';

const StyledContainer = styled(ActionDialog)`
  
`;

const CurrentLocationSection = styled.div`
  background-color: ${(props) => props.theme.colors.gray100 || '#f8f9fa'};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  border: 1px solid ${(props) => props.theme.colors.gray300 || '#dee2e6'};
  
  .location-icon {
    color: ${(props) => props.theme.colors.primary || '#007bff'};
    margin-right: 8px;
  }
  
  .current-location-text {
    font-size: 14px;
    color: ${(props) => props.theme.colors.gray600 || '#6c757d'};
    margin-bottom: 4px;
  }
  
  .location-name {
    font-size: 16px;
    font-weight: 600;
    color: ${(props) => props.theme.colors.dark || '#343a40'};
  }
`;

const LocationFeeSection = styled.div`
  background-color: ${(props) => props.theme.colors.warning100 || '#fff3cd'};
  border: 1px solid ${(props) => props.theme.colors.warning300 || '#ffeaa7'};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  
  .fee-icon {
    color: ${(props) => props.theme.colors.warning600 || '#d68910'};
    margin-right: 8px;
  }
  
  .fee-title {
    font-size: 14px;
    font-weight: 600;
    color: ${(props) => props.theme.colors.warning700 || '#b7791f'};
    margin-bottom: 8px;
  }
  
  .fee-description {
    font-size: 13px;
    color: ${(props) => props.theme.colors.warning700 || '#b7791f'};
    line-height: 1.4;
  }
`;

const FooterNote = styled.div`
  font-size: 12px;
  color: ${(props) => props.theme.colors.gray500 || '#6c757d'};
  text-align: center;
  margin-top: 16px;
  font-style: italic;
`;

class LocationPopup extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      initData: {
        location: '',
      },
    };
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.selectedIds !== undefined) {
      this.setState({
      });
    }
  }

  getCurrentLocation = () => {
    const { dataList, selectedIds } = this.props;
    if (!dataList || !selectedIds || selectedIds.length === 0) {
      return null;
    }
    
    const selectedProxy = dataList.find(item => item.uuid === selectedIds[0]);
    if (!selectedProxy) {
      return null;
    }
    
    // Lấy location hiện tại từ proxy data
    return get(selectedProxy, 'location') || get(selectedProxy, 'httpProxy.modem.location') || 'Unknown';
  }

  getLocationFee = () => {
    const { dataList, selectedIds } = this.props;
    if (!dataList || !selectedIds || selectedIds.length === 0) {
      return 5.00; // Default fee
    }
    
    const selectedProxy = dataList.find(item => item.uuid === selectedIds[0]);
    if (!selectedProxy) {
      return 5.00;
    }
    
    // Lấy locationFee từ salePackage
    return get(selectedProxy, 'salePackage.locationFee', 5.00);
  }

  handleSubmit = async (values, { setSubmitting }) => {
    const { forceRefresh, handleOnClose, intl, selectedIds } = this.props;

    const dataSubmit = {
      location: values.location,
      uuids: selectedIds,
    };

    const [err, response] = await TO(changeLocation(dataSubmit));
    if (err) {
      this.props.handleAlertError(intl.formatMessage(messages.msgUpdateFailed));
    }
    if (response.code === errorCode.SUCCESS) {
      this.props.handleAlertSuccess(intl.formatMessage(messages.msgUpdateSuccess));
      handleOnClose();
      forceRefresh();
    } else if (response.message) {
      this.props.handleAlertError(response.message);
    } else {
      this.props.handleAlertError(intl.formatMessage(messages.msgUpdateFailed));
    }
    setSubmitting(false);
  }

  render() {
    const self = this;
    const {
      intl,
      isOpen,
      handleOnClose,
      locations,
      modemType,
    } = this.props;

    const {
      initData,
    } = self.state;

    const currentLocation = this.getCurrentLocation();
    const locationFee = this.getLocationFee();

    let newlocations = locations;
    if (modemType !== undefined && modemType.length > 0) {
      newlocations = locations.filter((i) => i.modemType === modemType);
    }

    // Lọc bỏ currentLocation khỏi danh sách locations
    if (currentLocation) {
      newlocations = newlocations.filter((location) => {
        const locationName = location.name || location.nameVi || location.label;
        return locationName !== currentLocation;
      });
    }

    // Không truyền allLabel để bỏ option "All"
    const locationOptions = convertDropdownList(newlocations);

    return (
      <StyledContainer
        portalClassName="custom-portal"
        title={intl.formatMessage(messages.choose_location)}
        usePortal
        canOutsideClickClose
        canEscapeKeyClose
        isOpen={isOpen}
        onClose={handleOnClose}
        width={500}
      >
        <Wrapper className="m-4">
          {/* Current Location Section */}
          {currentLocation && (
            <CurrentLocationSection>
              <div className="current-location-text">
                <i className="fa fa-map-marker-alt location-icon" />
                {intl.formatMessage(messages.current_location)}
              </div>
              <div className="location-name">{currentLocation}</div>
            </CurrentLocationSection>
          )}

          <Formik
            onSubmit={this.handleSubmit}
            initialValues={initData}
            enableReinitialize
            validationSchema={Yup.object().shape({})}
            render={(props) => (
              <Form>
                <Row>
                  <Col md={{ size: 12 }}>
                    <DropdownList
                      label={intl.formatMessage(messages.select_new_location)}
                      value={locationOptions.find((option) =>
                        option.value === get(props.values, 'location')
                      )}
                      isAsterisk
                      name="location"
                      options={locationOptions}
                      onChange={(option) => {
                        props.setFieldValue('location', option.value);
                      }}
                    />
                  </Col>
                </Row>

                {/* Location Change Fee Section */}
                <LocationFeeSection>
                  <div className="fee-title">
                    <i className="fa fa-exclamation-circle fee-icon" />
                    {intl.formatMessage(messages.location_change_fee)}
                  </div>
                  <div className="fee-description">
                    {intl.formatMessage(messages.location_change_fee_description, { 
                      fee: `$${locationFee}` 
                    })}
                  </div>
                </LocationFeeSection>

                <div className="d-flex flex-column align-items-center">
                  <div className="d-flex">
                    <Button
                      primary
                      type="submit"
                      className="min-width-100 mt-4 mr-1"
                      loading={props.isSubmitting}
                    >
                      {intl.formatMessage(messages.change_location_button, { 
                        fee: `$${locationFee}` 
                      })}
                    </Button>
                  </div>
                  <ButtonLink
                    onClick={handleOnClose}
                    type={'button'}
                  >{intl.formatMessage(messages.cancel)}</ButtonLink>
                  
                  <FooterNote>
                    {intl.formatMessage(messages.location_change_note)}
                  </FooterNote>
                </div>
              </Form>
            )}
          />
        </Wrapper>
      </StyledContainer>
    );
  }
}

LocationPopup.propTypes = {};

const Wrapper = styled.div`
  margin-bottom: 10px;

  .content {
    border: 1px solid ${(props) => props.theme.colors.gray300};
    display: flex;
    flex-wrap: wrap;

    .label {
      color: ${(props) => props.theme.colors.black};
      font-size: ${(props) => props.theme.fontSizes.small12};
      opacity: 0.5;
    }

    .bold-text {
      color: ${(props) => props.theme.colors.black};
      font-size: ${(props) => props.theme.fontSizes.normal};
      font-weight: ${(props) => props.theme.fontWeights.strong};
      opacity: 0.8;
    }

    .group {
      padding: 10px 18px;
      background-color: ${(props) => props.theme.colors.white};
      width: 50%;

      &.gray {
        background-color: ${(props) => props.theme.colors.gray};
      }
    }
  }
`;

export default compose(
  WithHandleAlert,
  injectIntl
)(LocationPopup);
